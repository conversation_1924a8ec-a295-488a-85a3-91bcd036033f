import re

import requests


def normalize_name(name: str) -> str:
    name = re.sub(r"[\s\-]+", "_", name)
    name = re.sub(r"_+", "_", name)
    name = name.strip("_")
    return name


def context_component(data):
    url = "https://app-dev.rapidinnovation.dev/api/v1/components?refresh=true"
    components = requests.get(url).json()
    components = components[data["category"]]
    component = components[data["name"]]
    name = component["name"]
    description = component["description"]
    inputs = component["inputs"]
    outputs = component["outputs"]
    context = ""
    context += f"Name : {name}\nDescription : {description}\nOriginalType : {name}\nType : Component\n"
    context += "Inputs :-\n"
    for i in inputs:
        context += f"Input Name : {i['name']}\nInput Info : {i['info']}\nInput Type : {i['input_type']}\n"
        if i["input_types"]:
            context += "Input Types : " + ", ".join(i["input_types"]) + "\n"
        if i["required"]:
            context += "Required\n"
        if i["is_handle"]:
            context += "Handle\n"
        if i["is_list"]:
            context += "List\n"
        if i["real_time_refresh"]:
            context += "Real Time Refresh\n"
        if i["advanced"]:
            context += "Advanced\n"
        if i["value"]:
            context += f"Default Value : {i['value']}\n"
        if i["options"] is not None:
            context += "Options : " + ", ".join(i["options"]) + "\n"
        context += "\n"
    context += "Outputs :-\n"
    for o in outputs:
        context += f"Output Name : {o['name']}\nOutput Type : {o['output_type']}\n"
        if o["semantic_type"]:
            context += f"Semantic Type : {o['semantic_type']}\n"
        if o["method"]:
            context += f"Method : {o['method']}\n"
        context += "\n"
    return context


def workflow_context(data):
    url = (
        "https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows/" + data["id"]
    )
    workflow = requests.get(url).json()
    workflow = workflow["workflow"]
    name = workflow["name"]
    description = workflow["description"]
    inputs = workflow["start_nodes"]
    outputs = [
        {
            "name": "execution_status",
            "display_name": "Execution Status",
            "output_type": "string",
        },
        {
            "name": "workflow_execution_id",
            "display_name": "Execution ID",
            "output_type": "string",
        },
        {"name": "message", "display_name": "Message", "output_type": "string"},
    ]
    context = ""
    context += f"Name : {name}\nDescription : {description}\nOriginalType : workflow-{data["id"]}\nType : Workflow\n"
    context += "Inputs :-\n"
    for i in inputs:
        context += f"Input Name : {i['field']}\nInput Info : {i['field']}\nInput Type : {i['type']}\n"
        context += "Required\n"
        context += "Handle\n"
        context += "\n"
    context += "Outputs :-\n"
    for o in outputs:
        context += f"Output Name : {o['name']}\nOutput Type : {o['output_type']}\n"
        context += "\n"
    return context


def mcp_context(data):
    url = "https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps/" + data["id"]
    mcp = requests.get(url).json()
    tools = mcp["mcp"]["mcp_tools_config"]["tools"]
    for tool in tools:
        if tool["name"] == data["name"]:
            break
    mcp_name = mcp["mcp"]["name"]
    name = tool["name"]
    description = tool["description"]
    input_schema = tool["input_schema"]
    output_schema = tool["output_schema"]
    original_type = "MCP_" + normalize_name(f"{mcp_name} - {name}")
    context = ""
    context += f"Name : {name}\nDescription : {description}\nOriginalType : {original_type}\nType : MCP\nMCP_id : {data['id']}\n"
    context += "Inputs :-\n"
    required_input = input_schema["required"]
    defs = input_schema.get("$defs", {})
    properties = input_schema["properties"]
    for i in properties:
        property_details = properties[i]
        context += f"Input Name : {i}\n"
        if property_details.get("description"):
            context += f"Input Info : {property_details['description']}\n"
        property_scheme = None
        items = None
        options = None
        is_array = False
        if "type" in property_details:
            context += f"Input Type : {property_details['type']}\n"
            if property_details["type"] == "object":
                property_scheme = property_details["properties"]
            if property_details["type"] == "array":
                items = property_details["items"]
                is_array = True
        elif "anyOf" in property_details:
            context += "Input Type : "
            for j in property_details["anyOf"]:
                if "type" in j and j["type"] != "null":
                    context += j["type"] + "\n"
                    if j["type"] == "object":
                        property_scheme = j["properties"]
                    if j["type"] == "array":
                        items = j["items"]
                        is_array = True
                elif "$ref" in j:
                    ref = j["$ref"].split("/")[-1]
                    ref = defs[ref]
                    context += ref["type"] + "\n"
                    if "enum" in ref:
                        options = ref["enum"]
                    if ref["type"] == "object":
                        property_scheme = ref["properties"]
                    if ref["type"] == "array":
                        items = ref["items"]
                        is_array = True
        elif "$ref" in property_details:
            ref = property_details["$ref"].split("/")[-1]
            ref = defs[ref]
            context += f"Input Type : {ref['type']}\n"
            if "enum" in ref:
                options = ref["enum"]
            if ref["type"] == "object":
                property_scheme = ref["properties"]
            if ref["type"] == "array":
                items = ref["items"]
                is_array = True
        if i in required_input:
            context += "Required\n"
        context += "Handle\n"
        if is_array:
            context += "List\n"
        if property_details.get("default"):
            context += f"Default Value : {property_details['default']}\n"
        if options:
            context += "Options : " + ", ".join(options) + "\n"
        if property_scheme:
            context += "Properties :-\n"
            for j in property_scheme:
                context += f"> Property Name : {j}\n"
                if property_scheme[j].get("description"):
                    context += (
                        f"> Property Info : {property_scheme[j]['description']}\n"
                    )
                if property_scheme[j].get("type"):
                    context += f"> Property Type : {property_scheme[j]['type']}\n"
                if property_scheme[j].get("anyOf"):
                    context += "> Property Type : "
                    for k in property_scheme[j]["anyOf"]:
                        if "type" in k and k["type"] != "null":
                            context += k["type"] + ", "
                    context += "\n"
                if property_scheme[j].get("default"):
                    context += (
                        f"> Property Default Value : {property_scheme[j]['default']}\n"
                    )
                if property_scheme[j].get("enum"):
                    context += (
                        "> Property Options : "
                        + ", ".join(property_scheme[j]["enum"])
                        + "\n"
                    )
                context += "> \n"
        if items:
            context += f"Items : {items}\n"
        context += "\n"
    context += "Outputs :-\n"
    for o in output_schema["properties"]:
        context += f"Output Name : {o}\n"
        if output_schema["properties"][o].get("description"):
            context += (
                f"Output Info : {output_schema['properties'][o]['description']}\n"
            )
        if output_schema["properties"][o].get("type"):
            context += f"Output Type : {output_schema['properties'][o]['type']}\n"
        context += "\n"
    return context


context_helpers = {
    "component": context_component,
    "workflow": workflow_context,
    "mcp": mcp_context,
}
