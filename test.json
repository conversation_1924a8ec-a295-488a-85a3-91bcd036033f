{"workflow": {"nodes": [{"id": "start-node", "label": "Start", "type": "StartNode", "position": {"x": 100, "y": 100}, "dimension": {"width": 200, "height": 100}, "parameters": {}}, {"id": "MCP-12345", "label": "Generate Video Script", "type": "MCP_video-script-generation_video_script_generate", "position": {"x": 400, "y": 100}, "dimension": {"width": 250, "height": 150}, "parameters": {"video_time": 60}}, {"id": "MCP-23456", "label": "Generate Audio", "type": "MCP_voice-generation-mcp_generate_audio", "position": {"x": 750, "y": 100}, "dimension": {"width": 250, "height": 150}, "parameters": {"voice_id": "TX3LPaxmHKxFdv7VOQHJ", "provider": "elevenlabs"}}, {"id": "MCP-34567", "label": "Fetch Audio File", "type": "MCP_voice-generation-mcp_fetch_audio", "position": {"x": 1100, "y": 100}, "dimension": {"width": 250, "height": 150}, "parameters": {"provider": "elevenlabs"}}], "edges": [{"source": "start-node", "sourceHandle": "flow", "target": "MCP-12345", "targetHandle": "topic"}, {"source": "MCP-12345", "sourceHandle": "script", "target": "MCP-23456", "targetHandle": "script"}, {"source": "MCP-23456", "sourceHandle": "audio_ids", "target": "MCP-34567", "targetHandle": "audio_ids"}]}, "message": "I've created a workflow that generates a 1-minute video script from a topic, then uses Eleven Labs to create audio with the specified voice ID 'TX3LPaxmHKxFdv7VOQHJ', and finally fetches the generated audio file. The workflow requires you to provide the topic as input when you run it. The script duration is set to 60 seconds (1 minute) as requested."}