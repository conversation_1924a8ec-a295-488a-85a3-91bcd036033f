import copy
import json
import os

from pinecone import Pinecone
from sentence_transformers import SentenceTransformer
from strands import tool

from helper import context_helpers

########
# Loading any asserts or objects
########
db = json.load(open("assets/node_template.json"))
embedding_model = SentenceTransformer("mixedbread-ai/mxbai-embed-large-v1")
PINECONE_API_KEY = os.environ.get("PINECONE_API_KEY")
pc = Pinecone(api_key=PINECONE_API_KEY)
index = pc.Index("tool-embeddings")


########
# Helper functions or function which are nto tool but are needed.
########
def _return_node_template(node):
    template = copy.deepcopy(db[node["type"]])
    template["id"] = node["id"]
    template["position"] = node["position"]
    template["data"]["label"] = node["label"]
    template["data"]["config"] = node["parameters"]
    template["width"] = node["dimension"]["width"]
    template["height"] = node["dimension"]["height"]
    return template


def _return_edge_template(edge):
    template = {
        "animated": True,
        "style": {"strokeWidth": 2, "zIndex": 5},
        "id": "reactflow__edge-start-nodeflow-MCP_Google_Sheets_add_single_row-1753351326708row",
        "source": "start-node",
        "sourceHandle": "flow",
        "target": "MCP_Google_Sheets_add_single_row-1753351326708",
        "targetHandle": "row",
        "type": "default",
        "selected": False,
    }
    template.update(edge)
    template["id"] = (
        f"reactflow__edge{edge["source"]}{edge["sourceHandle"]}-{edge["target"]}{edge["targetHandle"]}"
    )
    return template


########
# Tools
########
@tool(
    name="get_current_workflow",
    description="Function take no input and return the current workflow in json string format.",
)
def get_current_workflow() -> str:
    return "{}"


@tool(
    name="RAG_search",
    description="Function take a description query and return the top k nodes which are semantically similar to the description. it return the list of dictionary which contains the type and description of the node. The default value of k is 10.",
)
def RAG_search(query: str, k: int = 10) -> list:
    embedding = embedding_model.encode(query).tolist()
    results = index.query(vector=embedding, top_k=k, include_metadata=True)
    results = results["matches"]
    output = [data["metadata"] for data in results]
    return output


@tool(
    name="get_context",
    description="Function take a item return by the RAG search and return the context, inputs and output of the node.",
)
def get_context(node_info: dict) -> str:
    return context_helpers[node_info["type"]](node_info)


def post_processing(json_str: str) -> str:
    workflow = json.loads(json_str)
    output = {}
    output["nodes"] = []
    output["edges"] = []
    for node in workflow["nodes"]:
        output["nodes"].append(_return_node_template(node))
    for edge in workflow["edges"]:
        output["edges"].append(_return_edge_template(edge))
    return json.dumps(output)


tools = [get_current_workflow, RAG_search, get_context]
