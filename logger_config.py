import json
import logging
import uuid
from datetime import datetime, timezone

SESSION_ID = str(uuid.uuid4())

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_record = {
            "timestamp": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
            "level": record.levelname,
            "session_id": SESSION_ID,
            "logger": record.name,
            "message": record.getMessage(),
        }
        return json.dumps(log_record)

# Global logger
logger = logging.getLogger("strands")
logger.setLevel(logging.INFO)

file_handler = logging.FileHandler("strands.jsonl")
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(JSONFormatter())
logger.addHandler(file_handler)
