{"StartNode": 0, "MCP_Script_Generation_script_generate": 1, "MCP_Voice_generation_generate_audio": 2, "MCP_Voice_generation_fetch_audio": 3, "MCP_Candidate_Interview_candidate_suitability_pre": 4, "ApiRequestNode": 5, "AgenticAI": 6, "MCP_Candidate_Interview_generate_interview_agenda": 7, "CombineTextComponent": 8, "MCP_content-extractor-mcp_generate_subtitle": 9, "MCP_Stock_Video_Generation_generate_stock_video": 10, "MCP_video-generation-mcp_generate_video": 11, "MCP_Stock_Image_Generation_generate_ai_stock_image": 12, "MCP_voice-generation-mcp_generate_audio": 13, "MCP_voice-generation-mcp_fetch_audio": 14, "MCP_script-generation-mcp-server_generate_script": 15, "DelayComponent": 16, "SelectDataComponent": 17, "LoopNode": 18, "UniversalConverterComponent": 19, "MCP_cinematic-video-generator_generate_video": 20, "ConditionalNode": 21, "MCP_stock-image-generation-mcp_generate_ai_stock_image": 22, "MCP_script-generation-mcp_script_generate": 23, "MCP_DuckDuckGo_fetch_content": 24, "MCP_context-engine-mcp_search": 25, "MCP_SlideSpeak_generate_powerpoint_slide_by_slide": 26, "MergeDataComponent": 27, "MCP_Leonardo_AI_Image_Generator_generateHeroImage": 28, "AlterMetadataComponent": 29, "MCP_Redis-mcp-01_set": 30, "MCP_Redis-mcp-01_get": 31, "MCP_Eraser_Diagram_Generator_generateDiagram": 32, "MCP_Content_Management_System_create_webflow_blog_post": 33, "MCP_Website_Generator_repo_setup": 34, "MCP_Tavily_Web_Search_and_Extraction_Server_tavily-extract": 35, "MCP_Website_Generator_create_file": 36, "MCP_Website_Generator_push_changes": 37, "MCP_Website_Generator_list_files": 38, "MCP_Website_Generator_read_file": 39, "MCP_Google_Sheets_find_row": 40, "MCP_Google_Sheets_update_cell": 41, "MCP_Google_Sheets_get_cell": 42, "MCP_Google_Document_create_document": 43, "MCP_Google_Sheets_create_worksheet": 44, "MCP_Google_Sheets_add_single_row": 45, "IDGeneratorComponent": 46, "MCP_Google_Forms_create_google_form": 47, "MCP_Google_Forms_update_google_form": 48, "MCP_Google_Document_update_document": 49, "MCP_Google_Document_append_document": 50, "MCP_Gmail_send_email": 51, "MCP_MetaAds_create_meta_campaign": 52, "MCP_MetaAds_create_adset": 53, "MCP_MetaAds_upload_ad_image": 54, "MCP_MetaAds_create_ad_creative": 55, "MCP_MetaAds_create_ad": 56, "workflow-3c17973e-646f-48a8-b84c-eb65f37646b5": 57, "workflow-d3ec861d-da29-4e5e-8c8e-db480d15d5cd": 58, "workflow-cf07c857-008c-44a7-b164-07f40cee8461": 59, "MCP_Leonardo_MCP_generateHeroImage": 60, "MCP_Redis-MCP_hset": 61, "MCP_Redis-MCP_hget": 62, "MCP_Redis-MCP_hgetall": 63, "MCP_CMS-MCP_create_webflow_blog_post": 64, "MCP_Redis-MCP_set": 65, "MCP_Redis-MCP_get": 66, "MCP_Google_Sheets_get_values_in_range": 67, "MCP_Google_Forms_get_google_form_responses": 68, "MCP_PDF_Reader_extract_metadata": 69, "MCP_PDF_Reader_extract_file_content": 70, "MCP_Redis_MCP_delete": 71, "SplitTextComponent": 72, "workflow-e840128c-5017-486a-9374-da5428dc0e6d": 73, "MCP_Google_Sheets_count_column_values": 74, "MCP_Google_Sheets_set_formula": 75, "MCP_Jira_&_Confluence_get_issue": 76, "MCP_Google_Sheets_update_row": 77, "MCP_Google_Document_get_document": 78, "MCP_Gmail_create_draft": 79, "MCP_SDR_Management_get_campaign": 80, "MCP_SDR_Management_create_customers": 81, "MCP_Apollo_IO_people_enrichment": 82, "MCP_Apollo_IO_people_search": 83, "MCP_SDR_Management_list_products": 84, "MCP_SDR_Management_create_email_conversation": 85, "MCP_SDR_Management_get_email_conversations": 86, "MCP_SDR_Management_reply_email_from_customer": 87, "MCP_SDR_Management_fetch_customer": 88}