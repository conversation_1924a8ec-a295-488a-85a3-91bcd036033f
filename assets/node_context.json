{"StartNode": {"name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "type": "StartNode", "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any"}], "node_info": "\nName : Start\nDescription : The starting point for all workflows. Only nodes connected to this node will be executed.\nType : StartNode\n", "input_info": "\nInputs : \n", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : flow\ndisplay_name : Flow\noutput_type : Any"}, "MCP_Script_Generation_script_generate": {"name": "Script Generation - script_generate", "description": "Provide topic and keyword to generator <PERSON><PERSON><PERSON>", "type": "MCP_Script_Generation_script_generate", "inputs": [{"name": "topic", "display_name": "Topic", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "script_type", "display_name": "script type", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "TOPIC", "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "keywords", "display_name": "keywords", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "video_type", "display_name": "video type", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "SHORT", "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "link", "display_name": "Link", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "title", "display_name": "title", "output_type": "string"}, {"name": "script", "display_name": "script", "output_type": "string"}, {"name": "script_type", "display_name": "script_type", "output_type": "string"}, {"name": "video_type", "display_name": "video_type", "output_type": "string"}, {"name": "link", "display_name": "link", "output_type": "string"}], "node_info": "\nName : Script Generation - script_generate\nDescription : Provide topic and keyword to generator Script\nType : MCP_Script_Generation_script_generate\n", "input_info": "\nInputs : \n\n----------------------\n\nName : topic\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : script_type\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\nDefault Value : TOPIC\n----------------------\n\nName : keywords\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : video_type\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\nDefault Value : SHORT\n----------------------\n\nName : link\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : title\ndisplay_name : title\noutput_type : string\n----------------------\n\nname : script\ndisplay_name : script\noutput_type : string\n----------------------\n\nname : script_type\ndisplay_name : script_type\noutput_type : string\n----------------------\n\nname : video_type\ndisplay_name : video_type\noutput_type : string\n----------------------\n\nname : link\ndisplay_name : link\noutput_type : string"}, "MCP_Voice_generation_generate_audio": {"name": "Voice generation - generate_audio", "description": "Generate video audio using the script", "type": "MCP_Voice_generation_generate_audio", "inputs": [{"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "info": "Script is required", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {"minLength": 1, "maxLength": 10000}}, {"name": "voice_id", "display_name": "Voice Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {"minLength": 1, "maxLength": 50}}, {"name": "provider", "display_name": "provider", "info": "Optional voice provider platform", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "elevenlabs", "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "audio_ids", "display_name": "audio_ids", "output_type": "list"}, {"name": "voice_id", "display_name": "voice_id", "output_type": "string"}, {"name": "audio_script", "display_name": "audio_script", "output_type": "string"}], "node_info": "\nName : Voice generation - generate_audio\nDescription : Generate video audio using the script\nType : MCP_Voice_generation_generate_audio\n", "input_info": "\nInputs : \n\n----------------------\n\nName : script\nInfo : Script is required\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : voice_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : provider\nInfo : Optional voice provider platform\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\nDefault Value : elevenlabs", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : audio_ids\ndisplay_name : audio_ids\noutput_type : list\n----------------------\n\nname : voice_id\ndisplay_name : voice_id\noutput_type : string\n----------------------\n\nname : audio_script\ndisplay_name : audio_script\noutput_type : string"}, "MCP_Voice_generation_fetch_audio": {"name": "Voice generation - fetch_audio", "description": "Fetch audio generated files links using ids", "type": "MCP_Voice_generation_fetch_audio", "inputs": [{"name": "audio_ids", "display_name": "Audio Ids", "info": "List of voice IDs is required", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "provider", "display_name": "provider", "info": "Optional voice provider platform", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "elevenlabs", "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "audio_urls", "display_name": "audio_urls", "output_type": "list"}, {"name": "mimetype", "display_name": "mimetype", "output_type": "string"}], "node_info": "\nName : Voice generation - fetch_audio\nDescription : Fetch audio generated files links using ids\nType : MCP_Voice_generation_fetch_audio\n", "input_info": "\nInputs : \n\n----------------------\n\nName : audio_ids\nInfo : List of voice IDs is required\nInput Type : array\nInput Types : ['array', 'Any']\nRequired\nHandle\nIs LIst\n----------------------\n\nName : provider\nInfo : Optional voice provider platform\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\nDefault Value : elevenlabs", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : audio_urls\ndisplay_name : audio_urls\noutput_type : list\n----------------------\n\nname : mimetype\ndisplay_name : mimetype\noutput_type : string"}, "MCP_Candidate_Interview_candidate_suitability_pre": {"name": "Candidate_Interview - candidate_suitability_pre", "description": "Analyze candidate suitability based on job description and resume links", "type": "MCP_Candidate_Interview_candidate_suitability_pre", "inputs": [{"name": "resume_s3_link", "display_name": "Resume S3 Link", "info": "S3 link to the candidate's resume", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {"minLength": 1, "maxLength": 2083}}, {"name": "job_description_s3_link", "display_name": "Job Description S3 Link", "info": "S3 link to the job description", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {"minLength": 1, "maxLength": 2083}}], "outputs": [{"name": "suitability_analysis", "display_name": "suitability_analysis", "output_type": "string"}, {"name": "resume_details", "display_name": "resume_details", "output_type": "string"}, {"name": "jd_details", "display_name": "jd_details", "output_type": "string"}], "node_info": "\nName : Candidate_Interview - candidate_suitability_pre\nDescription : Analyze candidate suitability based on job description and resume links\nType : MCP_Candidate_Interview_candidate_suitability_pre\n", "input_info": "\nInputs : \n\n----------------------\n\nName : resume_s3_link\nInfo : S3 link to the candidate's resume\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : job_description_s3_link\nInfo : S3 link to the job description\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : suitability_analysis\ndisplay_name : suitability_analysis\noutput_type : string\n----------------------\n\nname : resume_details\ndisplay_name : resume_details\noutput_type : string\n----------------------\n\nname : jd_details\ndisplay_name : jd_details\noutput_type : string"}, "ApiRequestNode": {"name": "API Request", "description": "Makes a single HTTP request to the specified URL.", "type": "ApiRequestNode", "inputs": [{"name": "url", "display_name": "URL", "info": "The URL to make the request to.", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "method", "display_name": "HTTP Method", "info": "The HTTP method to use for the request.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": true, "advanced": false, "value": "GET", "options": ["GET", "POST", "PUT", "PATCH", "DELETE"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query_params", "display_name": "Query Parameters", "info": "Key-value pairs to append to the URL query string (optional).", "input_type": "dict", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": true, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "headers", "display_name": "Request Headers", "info": "Key-value pairs for request headers (optional).", "input_type": "dict", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": true, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "body", "display_name": "Request Body (JSON)", "info": "Key-value dictionary for the request body (used for POST, PUT, PATCH). Will be JSON serialized.", "input_type": "dict", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": true, "value": {}, "options": null, "visibility_rules": [{"field_name": "method", "field_value": "POST", "operator": "equals"}, {"field_name": "method", "field_value": "PUT", "operator": "equals"}, {"field_name": "method", "field_value": "PATCH", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "method", "field_value": "POST", "operator": "equals"}, {"field_name": "method", "field_value": "PUT", "operator": "equals"}, {"field_name": "method", "field_value": "PATCH", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "timeout", "display_name": "Timeout (seconds)", "info": "Maximum time to wait for a response.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": 10, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "follow_redirects", "display_name": "Follow Redirects", "info": "Automatically follow HTTP redirects (e.g., 301, 302).", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "save_to_file", "display_name": "Save Response to File", "info": "Save response body to a temporary file instead of returning content directly. 'Result' output will be the file path.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": false, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_format", "display_name": "Output Format", "info": "'auto': Try JSON, fallback to text. 'json': Force JSON parsing (error if invalid). 'text': Return as text. 'bytes': Return raw bytes. 'file_path': Always save to file (requires 'Save Response to File'). 'metadata_dict': Return dict with status, headers, body/path, error.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "auto", "options": ["auto", "json", "text", "bytes", "file_path", "metadata_dict"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "raise_on_error", "display_name": "Raise Exception on HTTP Error", "info": "Stop workflow execution if an HTTP error status (4xx, 5xx) is received.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": false, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "data", "display_name": "Response Data/Body", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "status_code", "display_name": "Status Code", "output_type": "int", "semantic_type": null, "method": null}, {"name": "response_headers", "display_name": "Response Headers", "output_type": "dict", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "node_info": "\nName : API Request\nDescription : Makes a single HTTP request to the specified URL.\nType : ApiRequestNode\n", "input_info": "\nInputs : \n\n----------------------\n\nName : url\nInfo : The URL to make the request to.\nInput Type : string\nRequired\nHandle\nAdvanced\n----------------------\n\nName : method\nInfo : The HTTP method to use for the request.\nInput Type : dropdown\nReal Time Refresh\nOptions : ['GET', 'POST', 'PUT', 'PATCH', 'DELETE']\nDefault Value : GET\n----------------------\n\nName : query_params\nInfo : Key-value pairs to append to the URL query string (optional).\nInput Type : dict\nHandle\nAdvanced\n----------------------\n\nName : headers\nInfo : Key-value pairs for request headers (optional).\nInput Type : dict\nHandle\nAdvanced\n----------------------\n\nName : body\nInfo : Key-value dictionary for the request body (used for POST, PUT, PATCH). Will be JSON serialized.\nInput Type : dict\nHandle\nAdvanced\n----------------------\n\nName : timeout\nInfo : Maximum time to wait for a response.\nInput Type : int\nAdvanced\nDefault Value : 10\n----------------------\n\nName : follow_redirects\nInfo : Automatically follow HTTP redirects (e.g., 301, 302).\nInput Type : bool\nAdvanced\nDefault Value : True\n----------------------\n\nName : save_to_file\nInfo : Save response body to a temporary file instead of returning content directly. 'Result' output will be the file path.\nInput Type : bool\nAdvanced\n----------------------\n\nName : output_format\nInfo : 'auto': Try JSON, fallback to text. 'json': Force JSON parsing (error if invalid). 'text': Return as text. 'bytes': Return raw bytes. 'file_path': Always save to file (requires 'Save Response to File'). 'metadata_dict': Return dict with status, headers, body/path, error.\nInput Type : dropdown\nAdvanced\nOptions : ['auto', 'json', 'text', 'bytes', 'file_path', 'metadata_dict']\nDefault Value : auto\n----------------------\n\nName : raise_on_error\nInfo : Stop workflow execution if an HTTP error status (4xx, 5xx) is received.\nInput Type : bool\nAdvanced", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : data\ndisplay_name : Response Data/Body\noutput_type : Any\n----------------------\n\nname : status_code\ndisplay_name : Status Code\noutput_type : int\n----------------------\n\nname : response_headers\ndisplay_name : Response Headers\noutput_type : dict\n----------------------\n\nname : error\ndisplay_name : Error\noutput_type : str"}, "AgenticAI": {"name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "type": "AgenticAI", "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "api_key", "display_name": "API Key", "info": "API key for the model provider. Can be entered directly or referenced from secure storage.", "input_type": "credential", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR", "credential_type": "api_key", "use_credential_id": false, "credential_id": ""}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Determines if agent handles single response or multi-turn conversation.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "response", "options": ["response", "interactive"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "node_info": "\nName : AI Agent Executor\nDescription : Executes an AI agent with tools and memory using AutoGen.\nType : AgenticAI\n", "input_info": "\nInputs : \n\n----------------------\n\nName : model_provider\nInfo : The AI model provider to use.\nInput Type : dropdown\nOptions : ['OpenAI', 'Azure OpenAI', 'Anthropic', 'Claude', 'Google', 'Gemini', 'Mistral', 'Ollama', 'Custom']\nDefault Value : OpenAI\n----------------------\n\nName : base_url\nInfo : Base URL for the API (leave empty for default provider URL).\nInput Type : string\n----------------------\n\nName : api_key\nInfo : API key for the model provider. Can be entered directly or referenced from secure storage.\nInput Type : credential\n----------------------\n\nName : model_name\nInfo : Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.\nInput Type : dropdown\nOptions : ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo', 'claude-3-5-sonnet-20241022', 'claude-3-5-haiku-20241022', 'claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307', 'claude-2.1', 'claude-2.0', 'gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-pro', 'gemini-pro-vision', 'mistral-large-latest', 'mistral-medium-latest', 'mistral-small-latest', 'open-mistral-7b', 'open-mixtral-8x7b', 'open-mixtral-8x22b', 'llama3.2', 'llama3.1', 'llama3', 'llama2', 'mistral', 'mixtral', 'phi3', 'gemma', 'codellama', 'qwen2']\nDefault Value : gpt-4o\n----------------------\n\nName : temperature\nInfo : Controls randomness: 0 is deterministic, higher values are more random.\nInput Type : float\nDefault Value : 0.7\n----------------------\n\nName : description\nInfo : Description of the agent for UI display.\nInput Type : string\n----------------------\n\nName : execution_type\nInfo : Determines if agent handles single response or multi-turn conversation.\nInput Type : dropdown\nOptions : ['response', 'interactive']\nDefault Value : response\n----------------------\n\nName : query\nInfo : The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : system_message\nInfo : System prompt/instructions for the agent. If empty, will use default based on query.\nInput Type : string\n----------------------\n\nName : termination_condition\nInfo : Defines when multi-turn conversations should end. Required for interactive execution type.\nInput Type : string\n----------------------\n\nName : max_tokens\nInfo : Maximum response length in tokens.\nInput Type : int\nDefault Value : 1000\n----------------------\n\nName : input_variables\nInfo : Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.\nInput Type : dict\nInput Types : ['dict', 'Any']\nHandle\n----------------------\n\nName : tools\nInfo : Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.\nInput Type : handle\nInput Types : ['Any']\nHandle\n----------------------\n\nName : memory\nInfo : Connect a memory object from another node.\nInput Type : handle\nInput Types : ['Any']\nHandle\n----------------------\n\nName : autogen_agent_type\nInfo : The type of AutoGen agent to create internally.\nInput Type : dropdown\nAdvanced\nOptions : ['Assistant', 'UserProxy', 'CodeExecutor']\nDefault Value : Assistant", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : final_answer\ndisplay_name : Final Answer\noutput_type : string\n----------------------\n\nname : intermediate_steps\ndisplay_name : Intermediate Steps\noutput_type : list\n----------------------\n\nname : updated_memory\ndisplay_name : Updated Memory\noutput_type : Memory\n----------------------\n\nname : error\ndisplay_name : Error\noutput_type : str"}, "MCP_Candidate_Interview_generate_interview_agenda": {"name": "Candidate_Interview - generate_interview_agenda", "description": "Generate interview agenda based on job description and resume", "type": "MCP_Candidate_Interview_generate_interview_agenda", "inputs": [{"name": "resume_details", "display_name": "Resume Details", "info": " candidate's resume", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "jd_details", "display_name": "Jd Details", "info": "job description", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "prompt", "display_name": "Prompt", "info": "Optional custom prompt to guide the agenda generation", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "interview_agenda", "display_name": "interview_agenda", "output_type": "string"}, {"name": "resume_details", "display_name": "resume_details", "output_type": "string"}, {"name": "jd_details", "display_name": "jd_details", "output_type": "string"}], "node_info": "\nName : Candidate_Interview - generate_interview_agenda\nDescription : Generate interview agenda based on job description and resume\nType : MCP_Candidate_Interview_generate_interview_agenda\n", "input_info": "\nInputs : \n\n----------------------\n\nName : resume_details\nInfo :  candidate's resume\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : jd_details\nInfo : job description\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : prompt\nInfo : Optional custom prompt to guide the agenda generation\nInput Type : string\nInput Types : ['string', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : interview_agenda\ndisplay_name : interview_agenda\noutput_type : string\n----------------------\n\nname : resume_details\ndisplay_name : resume_details\noutput_type : string\n----------------------\n\nname : jd_details\ndisplay_name : jd_details\noutput_type : string"}, "CombineTextComponent": {"name": "Combine Text", "description": "Joins text inputs with a separator, supporting a variable number of inputs.", "type": "CombineTextComponent", "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main text or list to combine. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional text inputs to show (1-10).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 2, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "separator", "display_name": "Separator", "info": "The character or string to join the text with.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "\\n", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_1", "display_name": "Input 1", "info": "Text for input 1. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_2", "display_name": "Input 2", "info": "Text for input 2. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_3", "display_name": "Input 3", "info": "Text for input 3. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_4", "display_name": "Input 4", "info": "Text for input 4. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_5", "display_name": "Input 5", "info": "Text for input 5. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_6", "display_name": "Input 6", "info": "Text for input 6. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_7", "display_name": "Input 7", "info": "Text for input 7. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_8", "display_name": "Input 8", "info": "Text for input 8. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_9", "display_name": "Input 9", "info": "Text for input 9. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_10", "display_name": "Input 10", "info": "Text for input 10. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Combined Text", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "node_info": "\nName : Combine Text\nDescription : Joins text inputs with a separator, supporting a variable number of inputs.\nType : CombineTextComponent\n", "input_info": "\nInputs : \n\n----------------------\n\nName : main_input\nInfo : The main text or list to combine. Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['string', 'list', 'Any']\nHandle\n----------------------\n\nName : num_additional_inputs\nInfo : Set the number of additional text inputs to show (1-10).\nInput Type : int\nDefault Value : 2\n----------------------\n\nName : separator\nInfo : The character or string to join the text with.\nInput Type : string\nDefault Value : \\n\n----------------------\n\nName : input_1\nInfo : Text for input 1. Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : input_2\nInfo : Text for input 2. Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : input_3\nInfo : Text for input 3. Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : input_4\nInfo : Text for input 4. Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : input_5\nInfo : Text for input 5. Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : input_6\nInfo : Text for input 6. Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : input_7\nInfo : Text for input 7. Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : input_8\nInfo : Text for input 8. Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : input_9\nInfo : Text for input 9. Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : input_10\nInfo : Text for input 10. Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Combined Text\noutput_type : string\n----------------------\n\nname : error\ndisplay_name : Error\noutput_type : str"}, "MCP_content-extractor-mcp_generate_subtitle": {"name": "content-extractor-mcp - generate_subtitle", "description": "Generate subtitles for a video by processing its audio.", "type": "MCP_content-extractor-mcp_generate_subtitle", "inputs": [{"name": "audio_urls", "display_name": "Audio Urls", "info": "List of audio URLs is required", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "info": "Script is required", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {"minLength": 1, "maxLength": 1000}}], "outputs": [{"name": "subtitle", "display_name": "subtitle", "output_type": "string"}], "node_info": "\nName : content-extractor-mcp - generate_subtitle\nDescription : Generate subtitles for a video by processing its audio.\nType : MCP_content-extractor-mcp_generate_subtitle\n", "input_info": "\nInputs : \n\n----------------------\n\nName : audio_urls\nInfo : List of audio URLs is required\nInput Type : array\nInput Types : ['array', 'Any']\nRequired\nHandle\nIs LIst\n----------------------\n\nName : script\nInfo : Script is required\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : subtitle\ndisplay_name : subtitle\noutput_type : string"}, "MCP_Stock_Video_Generation_generate_stock_video": {"name": "Stock Video Generation - generate_stock_video", "description": "generate and find the stock video for the video", "type": "MCP_Stock_Video_Generation_generate_stock_video", "inputs": [{"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "info": "Script is required", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {"minLength": 1, "maxLength": 1000}}], "outputs": [{"name": "stock_video_clips", "display_name": "stock_video_clips", "output_type": "list"}], "node_info": "\nName : Stock Video Generation - generate_stock_video\nDescription : generate and find the stock video for the video\nType : MCP_Stock_Video_Generation_generate_stock_video\n", "input_info": "\nInputs : \n\n----------------------\n\nName : script\nInfo : Script is required\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : stock_video_clips\ndisplay_name : stock_video_clips\noutput_type : list"}, "MCP_video-generation-mcp_generate_video": {"name": "video-generation-mcp - generate_video", "description": "generate and process the video", "type": "MCP_video-generation-mcp_generate_video", "inputs": [{"name": "view_type", "display_name": "view type", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "stock_video_clips", "display_name": "Stock Video Clips", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "stock_image_clips", "display_name": "Stock Image Clips", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "event_stock_clips", "display_name": "Event Stock Clips", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "audio_urls", "display_name": "Audio Urls", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "avatar_video_urls", "display_name": "Avatar Video Urls", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "subtitles", "display_name": "Subtitles", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {"minLength": 1}}], "outputs": [{"name": "thumbnail", "display_name": "thumbnail", "output_type": "object"}, {"name": "video_link", "display_name": "video_link", "output_type": "object"}, {"name": "duration", "display_name": "duration", "output_type": "number"}], "node_info": "\nName : video-generation-mcp - generate_video\nDescription : generate and process the video\nType : MCP_video-generation-mcp_generate_video\n", "input_info": "\nInputs : \n\n----------------------\n\nName : view_type\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : stock_video_clips\nInfo : \nInput Type : array\nInput Types : ['array', 'Any']\nHandle\nIs LIst\n----------------------\n\nName : stock_image_clips\nInfo : \nInput Type : array\nInput Types : ['array', 'Any']\nHandle\nIs LIst\n----------------------\n\nName : event_stock_clips\nInfo : \nInput Type : array\nInput Types : ['array', 'Any']\nHandle\nIs LIst\n----------------------\n\nName : audio_urls\nInfo : \nInput Type : array\nInput Types : ['array', 'Any']\nRequired\nHandle\nIs LIst\n----------------------\n\nName : avatar_video_urls\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : subtitles\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : thumbnail\ndisplay_name : thumbnail\noutput_type : object\n----------------------\n\nname : video_link\ndisplay_name : video_link\noutput_type : object\n----------------------\n\nname : duration\ndisplay_name : duration\noutput_type : number"}, "MCP_Stock_Image_Generation_generate_ai_stock_image": {"name": "Stock Image Generation - generate_ai_stock_image", "description": "generate and find the stock image for the video", "type": "MCP_Stock_Image_Generation_generate_ai_stock_image", "inputs": [{"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "info": "Script is required", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {"minLength": 1, "maxLength": 1000}}, {"name": "view_type", "display_name": "View Type", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {"minLength": 1, "maxLength": 50}}], "outputs": [{"name": "generated_stock_image", "display_name": "generated_stock_image", "output_type": "list"}], "node_info": "\nName : Stock Image Generation - generate_ai_stock_image\nDescription : generate and find the stock image for the video\nType : MCP_Stock_Image_Generation_generate_ai_stock_image\n", "input_info": "\nInputs : \n\n----------------------\n\nName : script\nInfo : Script is required\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : view_type\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : generated_stock_image\ndisplay_name : generated_stock_image\noutput_type : list"}, "MCP_voice-generation-mcp_generate_audio": {"name": "voice-generation-mcp - generate_audio", "description": "Generate video audio using the script", "type": "MCP_voice-generation-mcp_generate_audio", "inputs": [{"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "info": "Script is required", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {"minLength": 1, "maxLength": 10000}}, {"name": "voice_id", "display_name": "Voice Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {"minLength": 1, "maxLength": 50}}, {"name": "provider", "display_name": "provider", "info": "Optional voice provider platform", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "elevenlabs", "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "audio_ids", "display_name": "audio_ids", "output_type": "list"}, {"name": "voice_id", "display_name": "voice_id", "output_type": "string"}, {"name": "audio_script", "display_name": "audio_script", "output_type": "string"}], "node_info": "\nName : voice-generation-mcp - generate_audio\nDescription : Generate video audio using the script\nType : MCP_voice-generation-mcp_generate_audio\n", "input_info": "\nInputs : \n\n----------------------\n\nName : script\nInfo : Script is required\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : voice_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : provider\nInfo : Optional voice provider platform\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\nDefault Value : elevenlabs", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : audio_ids\ndisplay_name : audio_ids\noutput_type : list\n----------------------\n\nname : voice_id\ndisplay_name : voice_id\noutput_type : string\n----------------------\n\nname : audio_script\ndisplay_name : audio_script\noutput_type : string"}, "MCP_voice-generation-mcp_fetch_audio": {"name": "voice-generation-mcp - fetch_audio", "description": "Fetch audio generated files links using ids", "type": "MCP_voice-generation-mcp_fetch_audio", "inputs": [{"name": "audio_ids", "display_name": "Audio Ids", "info": "List of voice IDs is required", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "provider", "display_name": "provider", "info": "Optional voice provider platform", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "elevenlabs", "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "audio_urls", "display_name": "audio_urls", "output_type": "list"}, {"name": "mimetype", "display_name": "mimetype", "output_type": "string"}], "node_info": "\nName : voice-generation-mcp - fetch_audio\nDescription : Fetch audio generated files links using ids\nType : MCP_voice-generation-mcp_fetch_audio\n", "input_info": "\nInputs : \n\n----------------------\n\nName : audio_ids\nInfo : List of voice IDs is required\nInput Type : array\nInput Types : ['array', 'Any']\nRequired\nHandle\nIs LIst\n----------------------\n\nName : provider\nInfo : Optional voice provider platform\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\nDefault Value : elevenlabs", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : audio_urls\ndisplay_name : audio_urls\noutput_type : list\n----------------------\n\nname : mimetype\ndisplay_name : mimetype\noutput_type : string"}, "MCP_script-generation-mcp-server_generate_script": {"name": "script-generation-mcp-server - generate_script", "description": "Generate a script using OpenAI GPT-4o.", "type": "MCP_script-generation-mcp-server_generate_script", "inputs": [{"name": "script_type", "display_name": "Script Type", "info": "Type of script to generate (e.g., 'Ad', 'Story', etc.)", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "tone", "display_name": "<PERSON><PERSON>", "info": "Tone of the script (e.g., 'Professional', 'Casual', etc.)", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "length", "display_name": "Length", "info": "Total script duration in seconds. Can be an integer or a string containing only digits.", "input_type": "int", "input_types": ["int", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "topic", "display_name": "Topic", "info": "Main Topic of the script.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "characters", "display_name": "Characters", "info": "Characters to include. Enter 'nil' to opt out.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "scene", "display_name": "Scene", "info": "Specific scene description to be included in the script. Enter 'nil' to opt out.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "cta", "display_name": "Cta", "info": "Call-to-action line. Enter 'nil' to opt out.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "scene_duration", "display_name": "Scene Duration", "info": "Duration in seconds for each scene/part. Can be an integer or a string containing only digits. If equal to length, only one part will be generated. Optional.", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "script", "display_name": "script", "output_type": "list"}, {"name": "audio_text", "display_name": "audio_text", "output_type": "string"}, {"name": "sound_effects", "display_name": "sound_effects", "output_type": "list"}, {"name": "title", "display_name": "title", "output_type": "string"}, {"name": "error", "display_name": "error", "output_type": "string"}], "node_info": "\nName : script-generation-mcp-server - generate_script\nDescription : Generate a script using OpenAI GPT-4o.\nType : MCP_script-generation-mcp-server_generate_script\n", "input_info": "\nInputs : \n\n----------------------\n\nName : script_type\nInfo : Type of script to generate (e.g., 'Ad', 'Story', etc.)\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : tone\nInfo : Tone of the script (e.g., 'Professional', 'Casual', etc.)\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : length\nInfo : Total script duration in seconds. Can be an integer or a string containing only digits.\nInput Type : int\nInput Types : ['int', 'Any']\nRequired\nHandle\n----------------------\n\nName : topic\nInfo : Main Topic of the script.\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : characters\nInfo : Characters to include. Enter 'nil' to opt out.\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : scene\nInfo : Specific scene description to be included in the script. Enter 'nil' to opt out.\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : cta\nInfo : Call-to-action line. Enter 'nil' to opt out.\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : scene_duration\nInfo : Duration in seconds for each scene/part. Can be an integer or a string containing only digits. If equal to length, only one part will be generated. Optional.\nInput Type : int\nInput Types : ['int', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : script\ndisplay_name : script\noutput_type : list\n----------------------\n\nname : audio_text\ndisplay_name : audio_text\noutput_type : string\n----------------------\n\nname : sound_effects\ndisplay_name : sound_effects\noutput_type : list\n----------------------\n\nname : title\ndisplay_name : title\noutput_type : string\n----------------------\n\nname : error\ndisplay_name : error\noutput_type : string"}, "DelayComponent": {"name": "Wait / Delay", "description": "Pauses the workflow execution for a set number of seconds.", "type": "DelayComponent", "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "The input data to be passed through.", "input_type": "string", "input_types": ["number", "string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "delay_seconds", "display_name": "Delay (seconds)", "info": "The number of seconds to pause the workflow.", "input_type": "string", "input_types": ["number", "string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "30", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "output", "display_name": "Output", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "Message", "display_name": "Message", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "string", "semantic_type": null, "method": null}], "node_info": "\nName : Wait / Delay\nDescription : Pauses the workflow execution for a set number of seconds.\nType : DelayComponent\n", "input_info": "\nInputs : \n\n----------------------\n\nName : input_data\nInfo : The input data to be passed through.\nInput Type : string\nInput Types : ['number', 'string', 'Any']\nRequired\nHandle\n----------------------\n\nName : delay_seconds\nInfo : The number of seconds to pause the workflow.\nInput Type : string\nInput Types : ['number', 'string', 'Any']\nRequired\nHandle\nDefault Value : 30", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : output\ndisplay_name : Output\noutput_type : Any\n----------------------\n\nname : Message\ndisplay_name : Message\noutput_type : string\n----------------------\n\nname : error\ndisplay_name : Error\noutput_type : string"}, "SelectDataComponent": {"name": "Select Data", "description": "Extracts elements from lists or dictionaries.", "type": "SelectDataComponent", "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "The data to select from. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["list", "dict", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "data_type", "display_name": "Data Type", "info": "The type of data structure to select from.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "Auto-Detect", "options": ["Auto-Detect", "List", "Dictionary"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "search_mode", "display_name": "Search Mode", "info": "Exact Path: Use precise path notation (e.g., 'user.name'). Smart Search: Find field by name anywhere in the structure (e.g., 'email' finds first occurrence).", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "Exact Path", "options": ["Exact Path", "Smart Search"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "field_matching_mode", "display_name": "Field Matching Mode", "info": "Auto-detect: Try key-based first, fallback to property-based. Key-based Only: Traditional object keys. Property-based Only: Search property_name fields. Use @ notation for property-based paths (e.g., 'result.@script').", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "Auto-detect", "options": ["Auto-detect", "Key-based Only", "Property-based Only"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "selector", "display_name": "Selector", "info": "Selector string: For lists: index or slice (e.g., '0', '1:5'). For dicts: key name or path (e.g., 'user.name') in Exact Path mode, or field name (e.g., 'email') in Smart Search mode. Use @ notation for property-based paths (e.g., 'result.@script'). Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "output_data", "display_name": "Selected Data", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "node_info": "\nName : Select Data\nDescription : Extracts elements from lists or dictionaries.\nType : SelectDataComponent\n", "input_info": "\nInputs : \n\n----------------------\n\nName : input_data\nInfo : The data to select from. Can be connected from another node or entered directly.\nInput Type : dict\nInput Types : ['list', 'dict', 'Any']\nRequired\nHandle\n----------------------\n\nName : data_type\nInfo : The type of data structure to select from.\nInput Type : dropdown\nOptions : ['Auto-Detect', 'List', 'Dictionary']\nDefault Value : Auto-Detect\n----------------------\n\nName : search_mode\nInfo : Exact Path: Use precise path notation (e.g., 'user.name'). Smart Search: Find field by name anywhere in the structure (e.g., 'email' finds first occurrence).\nInput Type : dropdown\nOptions : ['Exact Path', 'Smart Search']\nDefault Value : Exact Path\n----------------------\n\nName : field_matching_mode\nInfo : Auto-detect: Try key-based first, fallback to property-based. Key-based Only: Traditional object keys. Property-based Only: Search property_name fields. Use @ notation for property-based paths (e.g., 'result.@script').\nInput Type : dropdown\nOptions : ['Auto-detect', 'Key-based Only', 'Property-based Only']\nDefault Value : Auto-detect\n----------------------\n\nName : selector\nInfo : Selector string: For lists: index or slice (e.g., '0', '1:5'). For dicts: key name or path (e.g., 'user.name') in Exact Path mode, or field name (e.g., 'email') in Smart Search mode. Use @ notation for property-based paths (e.g., 'result.@script'). Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : output_data\ndisplay_name : Selected Data\noutput_type : Any\n----------------------\n\nname : error\ndisplay_name : Error\noutput_type : str"}, "LoopNode": {"name": "For Each Loop", "description": "Iterates over a list, with advanced controls for parallelism, aggregation, and error handling.", "type": "LoopNode", "inputs": [{"name": "source_type", "display_name": "Iteration Source", "info": "Choose whether to iterate over a list of items or a number range.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": true, "advanced": false, "value": "iteration_list", "options": ["iteration_list", "number_range"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "iteration_list", "display_name": "Iteration List", "info": "The array of items to be processed by the loop. Can be connected from another node or entered directly as JSON array.", "input_type": "list", "input_types": ["array", "list", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "iteration_list", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "source_type", "field_value": "iteration_list", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "batch_size", "display_name": "<PERSON><PERSON> Si<PERSON>", "info": "Number of items to process together in each iteration. For example, batch size 2 will process items in pairs: [item1, item2], [item3, item4], etc.", "input_type": "string", "input_types": ["number", "integer", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "1", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "iteration_list", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "start", "display_name": "Start Number", "info": "Starting number for the range. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["number", "integer", "string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "1", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "end", "display_name": "End Number", "info": "Ending number for the range (inclusive). Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["number", "integer", "string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "10", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "step", "display_name": "Step Size", "info": "Step size for the range (default: 1). Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["number", "integer", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "1", "options": null, "visibility_rules": [{"field_name": "source_type", "field_value": "number_range", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "parallel_execution", "display_name": "Parallel Execution", "info": "Execute loop iterations in parallel for better performance.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_concurrent", "display_name": "Max Concurrent Iterations", "info": "Maximum number of iterations to run concurrently (1-20).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 3, "options": null, "visibility_rules": [{"field_name": "parallel_execution", "field_value": "True", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "preserve_order", "display_name": "Preserve Order", "info": "Maintain the original order of items in the results.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "iteration_timeout", "display_name": "Iteration Timeout (seconds)", "info": "Maximum time to wait for each iteration to complete (1-3600 seconds).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": 60, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "aggregation_type", "display_name": "Result Aggregation", "info": "How to aggregate results from all iterations.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "collect_all", "options": ["collect_all", "collect_successful", "count_only", "latest_only", "first_success", "combine_text", "return_original"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "include_metadata", "display_name": "Include Metadata", "info": "Include metadata (timing, iteration index, etc.) in results.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "on_iteration_error", "display_name": "On Iteration Error", "info": "How to handle errors in individual iterations.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "continue", "options": ["continue", "retry_once", "retry_twice", "exit_loop"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "include_errors", "display_name": "Include Errors in Results", "info": "Include error information in the final results.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "current_item", "display_name": "Current Item (Iteration Output)", "output_type": "object", "semantic_type": null, "method": null}, {"name": "final_results", "display_name": "All Results (Exit Output)", "output_type": "array", "semantic_type": null, "method": null}], "node_info": "\nName : For Each Loop\nDescription : Iterates over a list, with advanced controls for parallelism, aggregation, and error handling.\nType : LoopNode\n", "input_info": "\nInputs : \n\n----------------------\n\nName : source_type\nInfo : Choose whether to iterate over a list of items or a number range.\nInput Type : dropdown\nReal Time Refresh\nOptions : ['iteration_list', 'number_range']\nDefault Value : iteration_list\n----------------------\n\nName : iteration_list\nInfo : The array of items to be processed by the loop. Can be connected from another node or entered directly as JSON array.\nInput Type : list\nInput Types : ['array', 'list', 'Any']\nHandle\nIs LIst\n----------------------\n\nName : batch_size\nInfo : Number of items to process together in each iteration. For example, batch size 2 will process items in pairs: [item1, item2], [item3, item4], etc.\nInput Type : string\nInput Types : ['number', 'integer', 'Any']\nHandle\nDefault Value : 1\n----------------------\n\nName : start\nInfo : Starting number for the range. Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['number', 'integer', 'string', 'Any']\nHandle\nDefault Value : 1\n----------------------\n\nName : end\nInfo : Ending number for the range (inclusive). Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['number', 'integer', 'string', 'Any']\nHandle\nDefault Value : 10\n----------------------\n\nName : step\nInfo : Step size for the range (default: 1). Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['number', 'integer', 'Any']\nHandle\nDefault Value : 1\n----------------------\n\nName : parallel_execution\nInfo : Execute loop iterations in parallel for better performance.\nInput Type : bool\nDefault Value : True\n----------------------\n\nName : max_concurrent\nInfo : Maximum number of iterations to run concurrently (1-20).\nInput Type : int\nDefault Value : 3\n----------------------\n\nName : preserve_order\nInfo : Maintain the original order of items in the results.\nInput Type : bool\nDefault Value : True\n----------------------\n\nName : iteration_timeout\nInfo : Maximum time to wait for each iteration to complete (1-3600 seconds).\nInput Type : int\nAdvanced\nDefault Value : 60\n----------------------\n\nName : aggregation_type\nInfo : How to aggregate results from all iterations.\nInput Type : dropdown\nOptions : ['collect_all', 'collect_successful', 'count_only', 'latest_only', 'first_success', 'combine_text', 'return_original']\nDefault Value : collect_all\n----------------------\n\nName : include_metadata\nInfo : Include metadata (timing, iteration index, etc.) in results.\nInput Type : bool\nAdvanced\nDefault Value : True\n----------------------\n\nName : on_iteration_error\nInfo : How to handle errors in individual iterations.\nInput Type : dropdown\nOptions : ['continue', 'retry_once', 'retry_twice', 'exit_loop']\nDefault Value : continue\n----------------------\n\nName : include_errors\nInfo : Include error information in the final results.\nInput Type : bool\nAdvanced\nDefault Value : True", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : current_item\ndisplay_name : Current Item (Iteration Output)\noutput_type : object\n----------------------\n\nname : final_results\ndisplay_name : All Results (Exit Output)\noutput_type : array"}, "UniversalConverterComponent": {"name": "Universal Converter", "description": "Convert data between different types (JSON, CSV, String, Number, Boolean, etc.)", "type": "UniversalConverterComponent", "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "The data to convert. Can be any type - string, object, array, number, etc. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "from_type", "display_name": "From Type", "info": "The current type of your input data. Auto-detect will determine this automatically.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "Auto-detect", "options": ["Auto-detect", "String", "Number", "Boolean", "Object", "Array", "<PERSON><PERSON>"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "to_type", "display_name": "To Type", "info": "The target type to convert to. JSON String: pretty-formatted JSON. CSV String: comma-separated values. Joined String: array elements joined with delimiter. Split Array: string split by delimiter. Flattened Object: nested object flattened to dot notation.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "String", "options": ["String", "Number", "Boolean", "Object", "Array", "JSON String", "CSV String", "Joined String", "Split Array", "Flattened Object"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "delimiter", "display_name": "Delimiter", "info": "Delimiter for CSV/Join/Split operations (e.g., ',', ';', '|', ' ', '\\t' for tab, '\\n' for newline)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ",", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "pretty_format", "display_name": "Pretty Format", "info": "For JSON String output, use pretty formatting with indentation and line breaks", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "converted_data", "display_name": "Converted Data", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "original_type", "display_name": "Original Type", "output_type": "string", "semantic_type": null, "method": null}, {"name": "target_type", "display_name": "Target Type", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "string", "semantic_type": null, "method": null}], "node_info": "\nName : Universal Converter\nDescription : Convert data between different types (JSON, CSV, String, Number, Boolean, etc.)\nType : UniversalConverterComponent\n", "input_info": "\nInputs : \n\n----------------------\n\nName : input_data\nInfo : The data to convert. Can be any type - string, object, array, number, etc. Can be connected from another node or entered directly.\nInput Type : multiline\nInput Types : ['Any']\nRequired\nHandle\n----------------------\n\nName : from_type\nInfo : The current type of your input data. Auto-detect will determine this automatically.\nInput Type : dropdown\nOptions : ['Auto-detect', 'String', 'Number', 'Boolean', 'Object', 'Array', 'Null']\nDefault Value : Auto-detect\n----------------------\n\nName : to_type\nInfo : The target type to convert to. JSON String: pretty-formatted JSON. CSV String: comma-separated values. Joined String: array elements joined with delimiter. Split Array: string split by delimiter. Flattened Object: nested object flattened to dot notation.\nInput Type : dropdown\nOptions : ['String', 'Number', 'Boolean', 'Object', 'Array', 'JSON String', 'CSV String', 'Joined String', 'Split Array', 'Flattened Object']\nDefault Value : String\n----------------------\n\nName : delimiter\nInfo : Delimiter for CSV/Join/Split operations (e.g., ',', ';', '|', ' ', '\\t' for tab, '\\n' for newline)\nInput Type : string\nDefault Value : ,\n----------------------\n\nName : pretty_format\nInfo : For JSON String output, use pretty formatting with indentation and line breaks\nInput Type : bool\nDefault Value : True", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : converted_data\ndisplay_name : Converted Data\noutput_type : Any\n----------------------\n\nname : original_type\ndisplay_name : Original Type\noutput_type : string\n----------------------\n\nname : target_type\ndisplay_name : Target Type\noutput_type : string\n----------------------\n\nname : error\ndisplay_name : Error\noutput_type : string"}, "MCP_cinematic-video-generator_generate_video": {"name": "cinematic-video-generator - generate_video", "description": "generate and process the video", "type": "MCP_cinematic-video-generator_generate_video", "inputs": [{"name": "view_type", "display_name": "VideoViewType", "info": "", "input_type": "dropdown", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": ["LANDSCAPE", "PORTRAIT", "SQUARE"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "scenes", "display_name": "Scenes", "info": "", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "audio_urls", "display_name": "Audio Urls", "info": "", "input_type": "array", "input_types": null, "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "subtitles", "display_name": "Subtitles", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "title", "display_name": "Title", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "sound_effects", "display_name": "Sound Effects", "info": "", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "info": "", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "thumbnail", "display_name": "thumbnail", "output_type": "object"}, {"name": "video_link", "display_name": "video link", "output_type": "object"}, {"name": "duration", "display_name": "duration", "output_type": "number"}], "node_info": "\nName : cinematic-video-generator - generate_video\nDescription : generate and process the video\nType : MCP_cinematic-video-generator_generate_video\n", "input_info": "\nInputs : \n\n----------------------\n\nName : view_type\nInfo : \nInput Type : dropdown\nRequired\nHandle\nOptions : ['LANDSCAPE', 'PORTRAIT', 'SQUARE']\n----------------------\n\nName : scenes\nInfo : \nInput Type : array\nHandle\nIs LIst\n----------------------\n\nName : audio_urls\nInfo : \nInput Type : array\nRequired\nHandle\nIs LIst\n----------------------\n\nName : subtitles\nInfo : \nInput Type : string\nRequired\nHandle\n----------------------\n\nName : title\nInfo : \nInput Type : string\nRequired\nHandle\n----------------------\n\nName : sound_effects\nInfo : \nInput Type : array\nHandle\nIs LIst\n----------------------\n\nName : script\nInfo : \nInput Type : array\nHandle\nIs LIst", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : thumbnail\ndisplay_name : thumbnail\noutput_type : object\n----------------------\n\nname : video_link\ndisplay_name : video link\noutput_type : object\n----------------------\n\nname : duration\ndisplay_name : duration\noutput_type : number"}, "ConditionalNode": {"name": "Switch-Case Router", "description": "Evaluates multiple conditions and routes data to matching outputs", "type": "ConditionalNode", "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "Input data that will be routed when conditions match. Can be connected from another node or entered directly.", "input_type": "multiline", "input_types": ["Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "source", "display_name": "Data Source", "info": "Select the data source for condition evaluation: 'node_output' uses connected input data, 'global_context' uses a global variable.", "input_type": "dropdown", "input_types": null, "required": true, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "node_output", "options": ["node_output", "global_context"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "variable_name", "display_name": "Variable Name", "info": "Name of the global variable to evaluate conditions against. This variable should be available in the workflow context.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "source", "field_value": "global_context", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": [{"field_name": "source", "field_value": "global_context", "operator": "equals"}], "requirement_logic": "OR"}, {"name": "condition_1_operator", "display_name": "Condition 1 - Operator", "info": "Comparison operator to apply", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_1_expected_value", "display_name": "Condition 1 - Expected Value", "info": "Value to compare against (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "condition_1_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_1_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_conditions", "display_name": "Number of Additional Conditions", "info": "Number of additional conditions beyond the base 1 condition (0-9).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "evaluation_strategy", "display_name": "Evaluation Strategy", "info": "Strategy for handling multiple conditions: 'first_match' stops at the first true condition, 'all_matches' executes all true conditions in parallel.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "all_matches", "options": ["first_match", "all_matches"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_2_operator", "display_name": "Condition 2 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "0", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_2_expected_value", "display_name": "Condition 2 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "0", "operator": "greater_than"}, {"field_name": "condition_2_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_2_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_3_operator", "display_name": "Condition 3 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_3_expected_value", "display_name": "Condition 3 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "1", "operator": "greater_than"}, {"field_name": "condition_3_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_3_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_4_operator", "display_name": "Condition 4 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_4_expected_value", "display_name": "Condition 4 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "2", "operator": "greater_than"}, {"field_name": "condition_4_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_4_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_5_operator", "display_name": "Condition 5 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_5_expected_value", "display_name": "Condition 5 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "3", "operator": "greater_than"}, {"field_name": "condition_5_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_5_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_6_operator", "display_name": "Condition 6 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_6_expected_value", "display_name": "Condition 6 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "4", "operator": "greater_than"}, {"field_name": "condition_6_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_6_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_7_operator", "display_name": "Condition 7 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_7_expected_value", "display_name": "Condition 7 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "5", "operator": "greater_than"}, {"field_name": "condition_7_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_7_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_8_operator", "display_name": "Condition 8 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_8_expected_value", "display_name": "Condition 8 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "6", "operator": "greater_than"}, {"field_name": "condition_8_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_8_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_9_operator", "display_name": "Condition 9 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_9_expected_value", "display_name": "Condition 9 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "7", "operator": "greater_than"}, {"field_name": "condition_9_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_9_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_10_operator", "display_name": "Condition 10 - Operator", "info": "Comparison operator to apply to the input data", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "equals", "options": ["equals", "not_equals", "contains", "starts_with", "ends_with", "greater_than", "less_than", "exists", "is_empty"], "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "greater_than"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "condition_10_expected_value", "display_name": "Condition 10 - Expected Value", "info": "Value to compare against the input data (not used for exists/is_empty operators)", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "num_additional_conditions", "field_value": "8", "operator": "greater_than"}, {"field_name": "condition_10_operator", "field_value": "exists", "operator": "not_equals"}, {"field_name": "condition_10_operator", "field_value": "is_empty", "operator": "not_equals"}], "visibility_logic": "AND", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "default", "display_name": "<PERSON><PERSON><PERSON>", "output_type": "Any", "semantic_type": null, "method": null}], "node_info": "\nName : Switch-Case Router\nDescription : Evaluates multiple conditions and routes data to matching outputs\nType : ConditionalNode\n", "input_info": "\nInputs : \n\n----------------------\n\nName : input_data\nInfo : Input data that will be routed when conditions match. Can be connected from another node or entered directly.\nInput Type : multiline\nInput Types : ['Any']\nRequired\nHandle\n----------------------\n\nName : source\nInfo : Select the data source for condition evaluation: 'node_output' uses connected input data, 'global_context' uses a global variable.\nInput Type : dropdown\nRequired\nOptions : ['node_output', 'global_context']\nDefault Value : node_output\n----------------------\n\nName : variable_name\nInfo : Name of the global variable to evaluate conditions against. This variable should be available in the workflow context.\nInput Type : string\n----------------------\n\nName : condition_1_operator\nInfo : Comparison operator to apply\nInput Type : dropdown\nOptions : ['equals', 'not_equals', 'contains', 'starts_with', 'ends_with', 'greater_than', 'less_than', 'exists', 'is_empty']\nDefault Value : equals\n----------------------\n\nName : condition_1_expected_value\nInfo : Value to compare against (not used for exists/is_empty operators)\nInput Type : string\n----------------------\n\nName : num_additional_conditions\nInfo : Number of additional conditions beyond the base 1 condition (0-9).\nInput Type : int\n----------------------\n\nName : evaluation_strategy\nInfo : Strategy for handling multiple conditions: 'first_match' stops at the first true condition, 'all_matches' executes all true conditions in parallel.\nInput Type : dropdown\nOptions : ['first_match', 'all_matches']\nDefault Value : all_matches\n----------------------\n\nName : condition_2_operator\nInfo : Comparison operator to apply to the input data\nInput Type : dropdown\nOptions : ['equals', 'not_equals', 'contains', 'starts_with', 'ends_with', 'greater_than', 'less_than', 'exists', 'is_empty']\nDefault Value : equals\n----------------------\n\nName : condition_2_expected_value\nInfo : Value to compare against the input data (not used for exists/is_empty operators)\nInput Type : string\n----------------------\n\nName : condition_3_operator\nInfo : Comparison operator to apply to the input data\nInput Type : dropdown\nOptions : ['equals', 'not_equals', 'contains', 'starts_with', 'ends_with', 'greater_than', 'less_than', 'exists', 'is_empty']\nDefault Value : equals\n----------------------\n\nName : condition_3_expected_value\nInfo : Value to compare against the input data (not used for exists/is_empty operators)\nInput Type : string\n----------------------\n\nName : condition_4_operator\nInfo : Comparison operator to apply to the input data\nInput Type : dropdown\nOptions : ['equals', 'not_equals', 'contains', 'starts_with', 'ends_with', 'greater_than', 'less_than', 'exists', 'is_empty']\nDefault Value : equals\n----------------------\n\nName : condition_4_expected_value\nInfo : Value to compare against the input data (not used for exists/is_empty operators)\nInput Type : string\n----------------------\n\nName : condition_5_operator\nInfo : Comparison operator to apply to the input data\nInput Type : dropdown\nOptions : ['equals', 'not_equals', 'contains', 'starts_with', 'ends_with', 'greater_than', 'less_than', 'exists', 'is_empty']\nDefault Value : equals\n----------------------\n\nName : condition_5_expected_value\nInfo : Value to compare against the input data (not used for exists/is_empty operators)\nInput Type : string\n----------------------\n\nName : condition_6_operator\nInfo : Comparison operator to apply to the input data\nInput Type : dropdown\nOptions : ['equals', 'not_equals', 'contains', 'starts_with', 'ends_with', 'greater_than', 'less_than', 'exists', 'is_empty']\nDefault Value : equals\n----------------------\n\nName : condition_6_expected_value\nInfo : Value to compare against the input data (not used for exists/is_empty operators)\nInput Type : string\n----------------------\n\nName : condition_7_operator\nInfo : Comparison operator to apply to the input data\nInput Type : dropdown\nOptions : ['equals', 'not_equals', 'contains', 'starts_with', 'ends_with', 'greater_than', 'less_than', 'exists', 'is_empty']\nDefault Value : equals\n----------------------\n\nName : condition_7_expected_value\nInfo : Value to compare against the input data (not used for exists/is_empty operators)\nInput Type : string\n----------------------\n\nName : condition_8_operator\nInfo : Comparison operator to apply to the input data\nInput Type : dropdown\nOptions : ['equals', 'not_equals', 'contains', 'starts_with', 'ends_with', 'greater_than', 'less_than', 'exists', 'is_empty']\nDefault Value : equals\n----------------------\n\nName : condition_8_expected_value\nInfo : Value to compare against the input data (not used for exists/is_empty operators)\nInput Type : string\n----------------------\n\nName : condition_9_operator\nInfo : Comparison operator to apply to the input data\nInput Type : dropdown\nOptions : ['equals', 'not_equals', 'contains', 'starts_with', 'ends_with', 'greater_than', 'less_than', 'exists', 'is_empty']\nDefault Value : equals\n----------------------\n\nName : condition_9_expected_value\nInfo : Value to compare against the input data (not used for exists/is_empty operators)\nInput Type : string\n----------------------\n\nName : condition_10_operator\nInfo : Comparison operator to apply to the input data\nInput Type : dropdown\nOptions : ['equals', 'not_equals', 'contains', 'starts_with', 'ends_with', 'greater_than', 'less_than', 'exists', 'is_empty']\nDefault Value : equals\n----------------------\n\nName : condition_10_expected_value\nInfo : Value to compare against the input data (not used for exists/is_empty operators)\nInput Type : string", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : default\ndisplay_name : Default\noutput_type : Any"}, "MCP_stock-image-generation-mcp_generate_ai_stock_image": {"name": "stock-image-generation-mcp - generate_ai_stock_image", "description": "generate and find the stock image for the video", "type": "MCP_stock-image-generation-mcp_generate_ai_stock_image", "inputs": [{"name": "script", "display_name": "<PERSON><PERSON><PERSON>", "info": "Script is required", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {"minLength": 1, "maxLength": 1000}}, {"name": "view_type", "display_name": "View Type", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {"minLength": 1, "maxLength": 50}}], "outputs": [{"name": "stock_image_clips", "display_name": "stock_image_clips", "output_type": "list"}], "node_info": "\nName : stock-image-generation-mcp - generate_ai_stock_image\nDescription : generate and find the stock image for the video\nType : MCP_stock-image-generation-mcp_generate_ai_stock_image\n", "input_info": "\nInputs : \n\n----------------------\n\nName : script\nInfo : Script is required\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : view_type\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : stock_image_clips\ndisplay_name : stock_image_clips\noutput_type : list"}, "MCP_script-generation-mcp_script_generate": {"name": "script-generation-mcp - script_generate", "description": "Provide topic and keyword to generator <PERSON><PERSON><PERSON>", "type": "MCP_script-generation-mcp_script_generate", "inputs": [{"name": "topic", "display_name": "Topic", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "script_type", "display_name": "script type", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "TOPIC", "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "keywords", "display_name": "keywords", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "video_type", "display_name": "video type", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "SHORT", "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "link", "display_name": "Link", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "title", "display_name": "title", "output_type": "string"}, {"name": "script", "display_name": "script", "output_type": "string"}, {"name": "script_type", "display_name": "script_type", "output_type": "string"}, {"name": "video_type", "display_name": "video_type", "output_type": "string"}, {"name": "link", "display_name": "link", "output_type": "string"}], "node_info": "\nName : script-generation-mcp - script_generate\nDescription : Provide topic and keyword to generator Script\nType : MCP_script-generation-mcp_script_generate\n", "input_info": "\nInputs : \n\n----------------------\n\nName : topic\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : script_type\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\nDefault Value : TOPIC\n----------------------\n\nName : keywords\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : video_type\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\nDefault Value : SHORT\n----------------------\n\nName : link\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : title\ndisplay_name : title\noutput_type : string\n----------------------\n\nname : script\ndisplay_name : script\noutput_type : string\n----------------------\n\nname : script_type\ndisplay_name : script_type\noutput_type : string\n----------------------\n\nname : video_type\ndisplay_name : video_type\noutput_type : string\n----------------------\n\nname : link\ndisplay_name : link\noutput_type : string"}, "MCP_DuckDuckGo_fetch_content": {"name": "DuckDuckGo - fetch_content", "description": "\n    Fetch and parse content from a webpage URL.\n\n    Args:\n        url: The webpage URL to fetch content from\n        ctx: MCP context for logging\n    ", "type": "MCP_DuckDuckGo_fetch_content", "inputs": [{"name": "url", "display_name": "Url", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "test", "display_name": "test", "output_type": "string"}], "node_info": "\nName : DuckDuckGo - fetch_content\nDescription : \n    Fetch and parse content from a webpage URL.\n\n    Args:\n        url: The webpage URL to fetch content from\n        ctx: MCP context for logging\n    \nType : MCP_DuckDuckGo_fetch_content\n", "input_info": "\nInputs : \n\n----------------------\n\nName : url\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\n<PERSON><PERSON>", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : test\ndisplay_name : test\noutput_type : string"}, "MCP_context-engine-mcp_search": {"name": "context-engine-mcp - search", "description": "Search for documents semantically similar to a query.", "type": "MCP_context-engine-mcp_search", "inputs": [{"name": "user_id", "display_name": "User Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "query_text", "display_name": "Query Text", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "organisation_id", "display_name": "Organisation Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "top_k", "display_name": "Top K", "info": "", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 10, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "agent_id", "display_name": "Agent Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "file_ids", "display_name": "File Ids", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "least_score", "display_name": "Least Score", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "success", "display_name": "success", "output_type": "boolean"}, {"name": "message", "display_name": "message", "output_type": "string"}, {"name": "results", "display_name": "results", "output_type": "list"}, {"name": "graph_context", "display_name": "graph context", "output_type": "object"}], "node_info": "\nName : context-engine-mcp - search\nDescription : Search for documents semantically similar to a query.\nType : MCP_context-engine-mcp_search\n", "input_info": "\nInputs : \n\n----------------------\n\nName : user_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : query_text\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : organisation_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : top_k\nInfo : \nInput Type : int\nInput Types : ['int', 'Any']\nHandle\nDefault Value : 10\n----------------------\n\nName : agent_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : file_ids\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : least_score\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : success\ndisplay_name : success\noutput_type : boolean\n----------------------\n\nname : message\ndisplay_name : message\noutput_type : string\n----------------------\n\nname : results\ndisplay_name : results\noutput_type : list\n----------------------\n\nname : graph_context\ndisplay_name : graph context\noutput_type : object"}, "MCP_SlideSpeak_generate_powerpoint_slide_by_slide": {"name": "SlideSpeak - generate_powerpoint_slide_by_slide", "description": "Generate a PowerPoint presentation slide by slide based on slides array and template using SlideSpeak", "type": "MCP_SlideSpeak_generate_powerpoint_slide_by_slide", "inputs": [{"name": "slides", "display_name": "Slides", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "template", "display_name": "Template", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "content", "display_name": "content", "output_type": "list"}, {"name": "isError", "display_name": "isError", "output_type": "boolean"}], "node_info": "\nName : SlideSpeak - generate_powerpoint_slide_by_slide\nDescription : Generate a PowerPoint presentation slide by slide based on slides array and template using SlideSpeak\nType : MCP_SlideSpeak_generate_powerpoint_slide_by_slide\n", "input_info": "\nInputs : \n\n----------------------\n\nName : slides\nInfo : \nInput Type : array\nInput Types : ['array', 'Any']\nRequired\nHandle\nIs LIst\n----------------------\n\nName : template\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : content\ndisplay_name : content\noutput_type : list\n----------------------\n\nname : isError\ndisplay_name : isError\noutput_type : boolean"}, "MergeDataComponent": {"name": "Merge Data", "description": "Combines multiple dictionaries or lists.", "type": "MergeDataComponent", "inputs": [{"name": "main_input", "display_name": "Main Input", "info": "The main data structure to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "num_additional_inputs", "display_name": "Number of Additional Inputs", "info": "Set the number of additional inputs to show (1-10).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 2, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "merge_strategy", "display_name": "Merge Strategy (Dicts)", "info": "How to handle conflicts when merging dictionaries. 'Aggregate' will combine conflicting values into a list. 'Structured Compose' creates custom key-value pairs.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "Overwrite", "options": ["Overwrite", "Deep Merge", "Error on Conflict", "Aggregate", "Structured Compose"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_1", "display_name": "Output Key 1", "info": "Custom key name for input 1 (e.g., 'data_1'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_2", "display_name": "Output Key 2", "info": "Custom key name for input 2 (e.g., 'data_2'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_3", "display_name": "Output Key 3", "info": "Custom key name for input 3 (e.g., 'data_3'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_4", "display_name": "Output Key 4", "info": "Custom key name for input 4 (e.g., 'data_4'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_5", "display_name": "Output Key 5", "info": "Custom key name for input 5 (e.g., 'data_5'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_6", "display_name": "Output Key 6", "info": "Custom key name for input 6 (e.g., 'data_6'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_7", "display_name": "Output Key 7", "info": "Custom key name for input 7 (e.g., 'data_7'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_8", "display_name": "Output Key 8", "info": "Custom key name for input 8 (e.g., 'data_8'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_9", "display_name": "Output Key 9", "info": "Custom key name for input 9 (e.g., 'data_9'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_10", "display_name": "Output Key 10", "info": "Custom key name for input 10 (e.g., 'data_10'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "output_key_11", "display_name": "Output Key 11", "info": "Custom key name for input 11 (e.g., 'data_11'). Only used with Structured Compose strategy.", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_1", "display_name": "Input 1", "info": "Data structure 1 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "1", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_2", "display_name": "Input 2", "info": "Data structure 2 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "2", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_3", "display_name": "Input 3", "info": "Data structure 3 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "3", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_4", "display_name": "Input 4", "info": "Data structure 4 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "4", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_5", "display_name": "Input 5", "info": "Data structure 5 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "5", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_6", "display_name": "Input 6", "info": "Data structure 6 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "6", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_7", "display_name": "Input 7", "info": "Data structure 7 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "7", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_8", "display_name": "Input 8", "info": "Data structure 8 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "8", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_9", "display_name": "Input 9", "info": "Data structure 9 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "9", "operator": "equals"}, {"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_10", "display_name": "Input 10", "info": "Data structure 10 to merge. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "list", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": [{"field_name": "num_additional_inputs", "field_value": "10", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "output_data", "display_name": "Merged Data", "output_type": "Any", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "node_info": "\nName : Merge Data\nDescription : Combines multiple dictionaries or lists.\nType : MergeDataComponent\n", "input_info": "\nInputs : \n\n----------------------\n\nName : main_input\nInfo : The main data structure to merge. Can be connected from another node or entered directly.\nInput Type : dict\nInput Types : ['dict', 'list', 'Any']\nRequired\nHandle\n----------------------\n\nName : num_additional_inputs\nInfo : Set the number of additional inputs to show (1-10).\nInput Type : int\nDefault Value : 2\n----------------------\n\nName : merge_strategy\nInfo : How to handle conflicts when merging dictionaries. 'Aggregate' will combine conflicting values into a list. 'Structured Compose' creates custom key-value pairs.\nInput Type : dropdown\nOptions : ['Overwrite', 'Deep Merge', 'Error on Conflict', 'Aggregate', 'Structured Compose']\nDefault Value : Overwrite\n----------------------\n\nName : output_key_1\nInfo : Custom key name for input 1 (e.g., 'data_1'). Only used with Structured Compose strategy.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : output_key_2\nInfo : Custom key name for input 2 (e.g., 'data_2'). Only used with Structured Compose strategy.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : output_key_3\nInfo : Custom key name for input 3 (e.g., 'data_3'). Only used with Structured Compose strategy.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : output_key_4\nInfo : Custom key name for input 4 (e.g., 'data_4'). Only used with Structured Compose strategy.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : output_key_5\nInfo : Custom key name for input 5 (e.g., 'data_5'). Only used with Structured Compose strategy.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : output_key_6\nInfo : Custom key name for input 6 (e.g., 'data_6'). Only used with Structured Compose strategy.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : output_key_7\nInfo : Custom key name for input 7 (e.g., 'data_7'). Only used with Structured Compose strategy.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : output_key_8\nInfo : Custom key name for input 8 (e.g., 'data_8'). Only used with Structured Compose strategy.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : output_key_9\nInfo : Custom key name for input 9 (e.g., 'data_9'). Only used with Structured Compose strategy.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : output_key_10\nInfo : Custom key name for input 10 (e.g., 'data_10'). Only used with Structured Compose strategy.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : output_key_11\nInfo : Custom key name for input 11 (e.g., 'data_11'). Only used with Structured Compose strategy.\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : input_1\nInfo : Data structure 1 to merge. Can be connected from another node or entered directly.\nInput Type : dict\nInput Types : ['dict', 'list', 'Any']\nHandle\n----------------------\n\nName : input_2\nInfo : Data structure 2 to merge. Can be connected from another node or entered directly.\nInput Type : dict\nInput Types : ['dict', 'list', 'Any']\nHandle\n----------------------\n\nName : input_3\nInfo : Data structure 3 to merge. Can be connected from another node or entered directly.\nInput Type : dict\nInput Types : ['dict', 'list', 'Any']\nHandle\n----------------------\n\nName : input_4\nInfo : Data structure 4 to merge. Can be connected from another node or entered directly.\nInput Type : dict\nInput Types : ['dict', 'list', 'Any']\nHandle\n----------------------\n\nName : input_5\nInfo : Data structure 5 to merge. Can be connected from another node or entered directly.\nInput Type : dict\nInput Types : ['dict', 'list', 'Any']\nHandle\n----------------------\n\nName : input_6\nInfo : Data structure 6 to merge. Can be connected from another node or entered directly.\nInput Type : dict\nInput Types : ['dict', 'list', 'Any']\nHandle\n----------------------\n\nName : input_7\nInfo : Data structure 7 to merge. Can be connected from another node or entered directly.\nInput Type : dict\nInput Types : ['dict', 'list', 'Any']\nHandle\n----------------------\n\nName : input_8\nInfo : Data structure 8 to merge. Can be connected from another node or entered directly.\nInput Type : dict\nInput Types : ['dict', 'list', 'Any']\nHandle\n----------------------\n\nName : input_9\nInfo : Data structure 9 to merge. Can be connected from another node or entered directly.\nInput Type : dict\nInput Types : ['dict', 'list', 'Any']\nHandle\n----------------------\n\nName : input_10\nInfo : Data structure 10 to merge. Can be connected from another node or entered directly.\nInput Type : dict\nInput Types : ['dict', 'list', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : output_data\ndisplay_name : Merged Data\noutput_type : Any\n----------------------\n\nname : error\ndisplay_name : Error\noutput_type : str"}, "MCP_Leonardo_AI_Image_Generator_generateHeroImage": {"name": "Leonardo AI Image Generator - generateHeroImage", "description": "Generate a hero image from text", "type": "MCP_Leonardo_AI_Image_Generator_generateHeroImage", "inputs": [{"name": "prompt", "display_name": "prompt", "info": "Text to generate image from", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Leonardo AI Image Generator - generateHeroImage\nDescription : Generate a hero image from text\nType : MCP_Leonardo_AI_Image_Generator_generateHeroImage\n", "input_info": "\nInputs : \n\n----------------------\n\nName : prompt\nInfo : Text to generate image from\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "AlterMetadataComponent": {"name": "<PERSON>er Metada<PERSON>", "description": "Modifies metadata dictionary keys.", "type": "AlterMetadataComponent", "inputs": [{"name": "input_metadata", "display_name": "Input Metadata", "info": "The metadata dictionary to modify. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "updates", "display_name": "<PERSON>ada<PERSON> Updates", "info": "Dictionary of key-value pairs to update or add to the metadata. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "keys_to_remove", "display_name": "Keys to Remove", "info": "List of keys to remove from the metadata. Can be connected from another node or entered directly.", "input_type": "list", "input_types": ["list", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": [], "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "output_metadata", "display_name": "Updated Metadata", "output_type": "dict", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "node_info": "\nName : Alter Metadata\nDescription : Modifies metadata dictionary keys.\nType : AlterMetadataComponent\n", "input_info": "\nInputs : \n\n----------------------\n\nName : input_metadata\nInfo : The metadata dictionary to modify. Can be connected from another node or entered directly.\nInput Type : dict\nInput Types : ['dict', 'Any']\nRequired\nHandle\n----------------------\n\nName : updates\nInfo : Dictionary of key-value pairs to update or add to the metadata. Can be connected from another node or entered directly.\nInput Type : dict\nInput Types : ['dict', 'Any']\nHandle\n----------------------\n\nName : keys_to_remove\nInfo : List of keys to remove from the metadata. Can be connected from another node or entered directly.\nInput Type : list\nInput Types : ['list', 'Any']\nHandle\nIs LIst", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : output_metadata\ndisplay_name : Updated Metadata\noutput_type : dict\n----------------------\n\nname : error\ndisplay_name : Error\noutput_type : str"}, "MCP_Redis-mcp-01_set": {"name": "Redis-mcp-01 - set", "description": "Set a Redis string value with an optional expiration time.\n\nArgs:\n    key (str): The key to set.\n    value (str): The value to store.\n    expiration (int, optional): Expiration time in seconds.\n\nReturns:\n    str: Confirmation message or an error message.\n", "type": "MCP_Redis-mcp-01_set", "inputs": [{"name": "key", "display_name": "Key", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "value", "display_name": "Value", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "expiration", "display_name": "Expiration", "info": "", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Redis-mcp-01 - set\nDescription : Set a Redis string value with an optional expiration time.\n\nArgs:\n    key (str): The key to set.\n    value (str): The value to store.\n    expiration (int, optional): Expiration time in seconds.\n\nReturns:\n    str: Confirmation message or an error message.\n\nType : MCP_Redis-mcp-01_set\n", "input_info": "\nInputs : \n\n----------------------\n\nName : key\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : value\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : expiration\nInfo : \nInput Type : int\nInput Types : ['int', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Redis-mcp-01_get": {"name": "Redis-mcp-01 - get", "description": "Get a Redis string value.\n\nArgs:\n    key (str): The key to retrieve.\n\nReturns:\n    str: The stored value or an error message.\n", "type": "MCP_Redis-mcp-01_get", "inputs": [{"name": "key", "display_name": "Key", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Redis-mcp-01 - get\nDescription : Get a Redis string value.\n\nArgs:\n    key (str): The key to retrieve.\n\nReturns:\n    str: The stored value or an error message.\n\nType : MCP_Redis-mcp-01_get\n", "input_info": "\nInputs : \n\n----------------------\n\nName : key\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\n<PERSON><PERSON>", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Eraser_Diagram_Generator_generateDiagram": {"name": "Eraser Diagram Generator - generateDiagram", "description": "Generate a diagram from text description using Eraser API", "type": "MCP_Eraser_Diagram_Generator_generateDiagram", "inputs": [{"name": "prompt", "display_name": "prompt", "info": "Text description of the diagram to generate", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Eraser Diagram Generator - generateDiagram\nDescription : Generate a diagram from text description using Eraser API\nType : MCP_Eraser_Diagram_Generator_generateDiagram\n", "input_info": "\nInputs : \n\n----------------------\n\nName : prompt\nInfo : Text description of the diagram to generate\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Content_Management_System_create_webflow_blog_post": {"name": "Content Management System - create_webflow_blog_post", "description": "Create a blog post in Webflow. This tool automatically analyzes the blog post content, creates the necessary collection fields if they don't exist, and posts the blog content. No need to specify collection fields - they are generated automatically based on the blog post data.", "type": "MCP_Content_Management_System_create_webflow_blog_post", "inputs": [{"name": "webflowToken", "display_name": "webflowToken", "info": "Webflow API token", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "siteId", "display_name": "siteId", "info": "Webflow site ID", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "collectionName", "display_name": "collectionName", "info": "Name of the blog collection", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "blogPost", "display_name": "blogPost", "info": "Blog post data", "input_type": "object", "input_types": ["object", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "publishAfterCreation", "display_name": "publishAfterCreation", "info": "Whether to publish the site after creating the post", "input_type": "bool", "input_types": ["bool", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "customDomains", "display_name": "customDomains", "info": "Custom domains to publish to", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Content Management System - create_webflow_blog_post\nDescription : Create a blog post in Webflow. This tool automatically analyzes the blog post content, creates the necessary collection fields if they don't exist, and posts the blog content. No need to specify collection fields - they are generated automatically based on the blog post data.\nType : MCP_Content_Management_System_create_webflow_blog_post\n", "input_info": "\nInputs : \n\n----------------------\n\nName : webflowToken\nInfo : Webflow API token\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : siteId\nInfo : Webflow site ID\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : collectionName\nInfo : Name of the blog collection\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : blogPost\nInfo : Blog post data\nInput Type : object\nInput Types : ['object', 'Any']\nRequired\nHandle\n----------------------\n\nName : publishAfterCreation\nInfo : Whether to publish the site after creating the post\nInput Type : bool\nInput Types : ['bool', 'Any']\nHandle\n----------------------\n\nName : customDomains\nInfo : Custom domains to publish to\nInput Type : array\nInput Types : ['array', 'Any']\nHandle\nIs LIst", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Website_Generator_repo_setup": {"name": "Website Generator - repo_setup", "description": "\n    Setup a new repository for a website project by cloning a template, creating a GitHub repo, and pushing the code.\n\n    This tool performs the following steps:\n    1. <PERSON>lone the React Vite template repository directly to base directory\n    2. Remove the .git folder from the cloned repository\n    3. Create a new repository on GitHub with the given project name\n    4. Push the code to the new GitHub repository\n    5. Optionally deploy to AWS Amplify (if requested)\n\n    Args:\n        project_name: Name of the project and GitHub repository\n        description: Optional description for the GitHub repository\n        deploy_to_amplify: Whether to deploy to AWS Amplify (optional, not implemented yet)\n\n    Returns:\n        Status message with repository URL and deployment information\n    ", "type": "MCP_Website_Generator_repo_setup", "inputs": [{"name": "project_name", "display_name": "Project Name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "description", "display_name": "Description", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "deploy_to_amplify", "display_name": "Deploy To Amplify", "info": "", "input_type": "bool", "input_types": ["bool", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "string"}], "node_info": "\nName : Website Generator - repo_setup\nDescription : \n    Setup a new repository for a website project by cloning a template, creating a GitHub repo, and pushing the code.\n\n    This tool performs the following steps:\n    1. Clone the React Vite template repository directly to base directory\n    2. Remove the .git folder from the cloned repository\n    3. Create a new repository on GitHub with the given project name\n    4. Push the code to the new GitHub repository\n    5. Optionally deploy to AWS Amplify (if requested)\n\n    Args:\n        project_name: Name of the project and GitHub repository\n        description: Optional description for the GitHub repository\n        deploy_to_amplify: Whether to deploy to AWS Amplify (optional, not implemented yet)\n\n    Returns:\n        Status message with repository URL and deployment information\n    \nType : MCP_Website_Generator_repo_setup\n", "input_info": "\nInputs : \n\n----------------------\n\nName : project_name\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : description\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : deploy_to_amplify\nInfo : \nInput Type : bool\nInput Types : ['bool', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : string"}, "MCP_Tavily_Web_Search_and_Extraction_Server_tavily-extract": {"name": "Tavily Web Search and Extraction Server - tavily-extract", "description": "A powerful web content extraction tool that retrieves and processes raw content from specified URLs, ideal for data collection, content analysis, and research tasks.", "type": "MCP_<PERSON>ly_Web_Search_and_Extraction_Server_tavily-extract", "inputs": [{"name": "urls", "display_name": "urls", "info": "List of URLs to extract content from", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "extract_depth", "display_name": "extract depth", "info": "Depth of extraction - 'basic' or 'advanced', if usrls are linkedin use 'advanced' or if explicitly told to use advanced", "input_type": "dropdown", "input_types": ["dropdown", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "basic", "options": ["basic", "advanced"], "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "include_images", "display_name": "include images", "info": "Include a list of images extracted from the urls in the response", "input_type": "bool", "input_types": ["bool", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "generated_string_output", "display_name": "generated_string_output", "output_type": "string"}], "node_info": "\nName : Tavily Web Search and Extraction Server - tavily-extract\nDescription : A powerful web content extraction tool that retrieves and processes raw content from specified URLs, ideal for data collection, content analysis, and research tasks.\nType : MCP_Tavily_Web_Search_and_Extraction_Server_tavily-extract\n", "input_info": "\nInputs : \n\n----------------------\n\nName : urls\nInfo : List of URLs to extract content from\nInput Type : array\nInput Types : ['array', 'Any']\nRequired\nHandle\nIs LIst\n----------------------\n\nName : extract_depth\nInfo : Depth of extraction - 'basic' or 'advanced', if usrls are linkedin use 'advanced' or if explicitly told to use advanced\nInput Type : dropdown\nInput Types : ['dropdown', 'Any']\nHandle\nOptions : ['basic', 'advanced']\nDefault Value : basic\n----------------------\n\nName : include_images\nInfo : Include a list of images extracted from the urls in the response\nInput Type : bool\nInput Types : ['bool', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : generated_string_output\ndisplay_name : generated_string_output\noutput_type : string"}, "MCP_Website_Generator_create_file": {"name": "Website Generator - create_file", "description": "\n    Create a new file in the project with content generated by the AI Agent.\n\n    This tool performs the following steps:\n    1. Get the file_name, file_path and content from the AI Agent\n    2. Create a new file with the given file_name at the file_path\n    3. Write the content in the file\n\n    Args:\n        file_name: Name of the file to create (e.g., \"index.html\", \"app.js\")\n        file_path: Directory path where the file should be created (e.g., \"./src\", \"./public\")\n        content: Content to write to the file\n\n    Returns:\n        Status message indicating success or failure\n    ", "type": "MCP_Website_Generator_create_file", "inputs": [{"name": "file_name", "display_name": "File Name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "file_path", "display_name": "File Path", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "content", "display_name": "Content", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "string"}], "node_info": "\nName : Website Generator - create_file\nDescription : \n    Create a new file in the project with content generated by the AI Agent.\n\n    This tool performs the following steps:\n    1. Get the file_name, file_path and content from the AI Agent\n    2. Create a new file with the given file_name at the file_path\n    3. Write the content in the file\n\n    Args:\n        file_name: Name of the file to create (e.g., \"index.html\", \"app.js\")\n        file_path: Directory path where the file should be created (e.g., \"./src\", \"./public\")\n        content: Content to write to the file\n\n    Returns:\n        Status message indicating success or failure\n    \nType : MCP_Website_Generator_create_file\n", "input_info": "\nInputs : \n\n----------------------\n\nName : file_name\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : file_path\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : content\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : string"}, "MCP_Website_Generator_push_changes": {"name": "Website Generator - push_changes", "description": "\n    Commit the changes made to the project and push them to the remote repository.\n\n    This tool performs the following steps:\n    1. Get the project name from the AI Agent\n    2. Locate the project directory in the base directory\n    3. Check if there are any changes made to the project\n    4. If there are no changes, it will return\n    5. If there are changes, it will stage the changes\n    6. Commit the changes with an auto-generated commit message\n    7. Push the changes to the remote repository\n\n    Args:\n        project_name: Name of the project (same as used in repo_setup)\n\n    Returns:\n        Status message indicating success or failure\n    ", "type": "MCP_Website_Generator_push_changes", "inputs": [{"name": "project_name", "display_name": "Project Name", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "string"}], "node_info": "\nName : Website Generator - push_changes\nDescription : \n    Commit the changes made to the project and push them to the remote repository.\n\n    This tool performs the following steps:\n    1. Get the project name from the AI Agent\n    2. Locate the project directory in the base directory\n    3. Check if there are any changes made to the project\n    4. If there are no changes, it will return\n    5. If there are changes, it will stage the changes\n    6. Commit the changes with an auto-generated commit message\n    7. Push the changes to the remote repository\n\n    Args:\n        project_name: Name of the project (same as used in repo_setup)\n\n    Returns:\n        Status message indicating success or failure\n    \nType : MCP_Website_Generator_push_changes\n", "input_info": "\nInputs : \n\n----------------------\n\nName : project_name\nInfo : \nInput Type : string\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : string"}, "MCP_Website_Generator_list_files": {"name": "Website Generator - list_files", "description": "\n    List all files and directories in the specified directory.\n\n    This tool provides a directory listing showing files and subdirectories.\n    Files are marked with [FILE] and directories with [DIR] prefixes.\n\n    Args:\n        directory_path: Path to the directory to list (can be relative or absolute)\n\n    Returns:\n        Directory listing or error message\n    ", "type": "MCP_Website_Generator_list_files", "inputs": [{"name": "directory_path", "display_name": "Directory Path", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "string"}], "node_info": "\nName : Website Generator - list_files\nDescription : \n    List all files and directories in the specified directory.\n\n    This tool provides a directory listing showing files and subdirectories.\n    Files are marked with [FILE] and directories with [DIR] prefixes.\n\n    Args:\n        directory_path: Path to the directory to list (can be relative or absolute)\n\n    Returns:\n        Directory listing or error message\n    \nType : MCP_Website_Generator_list_files\n", "input_info": "\nInputs : \n\n----------------------\n\nName : directory_path\nInfo : \nInput Type : string\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : string"}, "MCP_Website_Generator_read_file": {"name": "Website Generator - read_file", "description": "\n    Read the contents of a file.\n\n    This tool allows reading the contents of any file in the project or system.\n    Useful for examining configuration files, source code, or any text-based files.\n\n    Args:\n        file_path: Path to the file to read (can be relative or absolute)\n\n    Returns:\n        File contents or error message\n    ", "type": "MCP_Website_Generator_read_file", "inputs": [{"name": "file_path", "display_name": "File Path", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "string"}], "node_info": "\nName : Website Generator - read_file\nDescription : \n    Read the contents of a file.\n\n    This tool allows reading the contents of any file in the project or system.\n    Useful for examining configuration files, source code, or any text-based files.\n\n    Args:\n        file_path: Path to the file to read (can be relative or absolute)\n\n    Returns:\n        File contents or error message\n    \nType : MCP_Website_Generator_read_file\n", "input_info": "\nInputs : \n\n----------------------\n\nName : file_path\nInfo : \nInput Type : string\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : string"}, "MCP_Google_Sheets_find_row": {"name": "Google Sheets - find_row", "description": "Find one or more rows by a column and value", "type": "MCP_Google_Sheets_find_row", "inputs": [{"name": "spreadsheet_id", "display_name": "Spreadsheet Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "worksheet_name", "display_name": "Worksheet Name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "search_column", "display_name": "Search Column", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "search_value", "display_name": "Search Value", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "return_first_match", "display_name": "Return First Match", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Google Sheets - find_row\nDescription : Find one or more rows by a column and value\nType : MCP_Google_Sheets_find_row\n", "input_info": "\nInputs : \n\n----------------------\n\nName : spreadsheet_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : worksheet_name\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : search_column\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : search_value\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : return_first_match\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\nDefault Value : True", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Google_Sheets_update_cell": {"name": "Google Sheets - update_cell", "description": "Update a cell in a spreadsheet", "type": "MCP_Google_Sheets_update_cell", "inputs": [{"name": "spreadsheet_id", "display_name": "Spreadsheet Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "worksheet_name", "display_name": "Worksheet Name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "cell", "display_name": "Cell", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "value", "display_name": "Value", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Google Sheets - update_cell\nDescription : Update a cell in a spreadsheet\nType : MCP_Google_Sheets_update_cell\n", "input_info": "\nInputs : \n\n----------------------\n\nName : spreadsheet_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : worksheet_name\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : cell\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : value\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Google_Sheets_get_cell": {"name": "Google Sheets - get_cell", "description": "Fetch the contents of a specific cell in a spreadsheet", "type": "MCP_Google_Sheets_get_cell", "inputs": [{"name": "spreadsheet_id", "display_name": "Spreadsheet Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "worksheet_name", "display_name": "Worksheet Name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "cell", "display_name": "Cell", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Google Sheets - get_cell\nDescription : Fetch the contents of a specific cell in a spreadsheet\nType : MCP_Google_Sheets_get_cell\n", "input_info": "\nInputs : \n\n----------------------\n\nName : spreadsheet_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : worksheet_name\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : cell\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Google_Document_create_document": {"name": "Google Document - create_document", "description": "Create a new Google Document with optional title and content. Supports plain text, HTML, and Markdown formats.", "type": "MCP_Google_Document_create_document", "inputs": [{"name": "title", "display_name": "Title", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "content", "display_name": "Content", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "format", "display_name": "Format", "info": "", "input_type": "dropdown", "input_types": ["dropdown", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "plain", "options": ["plain", "html", "markdown"], "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Google Document - create_document\nDescription : Create a new Google Document with optional title and content. Supports plain text, HTML, and Markdown formats.\nType : MCP_Google_Document_create_document\n", "input_info": "\nInputs : \n\n----------------------\n\nName : title\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : content\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : format\nInfo : \nInput Type : dropdown\nInput Types : ['dropdown', 'Any']\nHandle\nOptions : ['plain', 'html', 'markdown']\nDefault Value : plain", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Google_Sheets_create_worksheet": {"name": "Google Sheets - create_worksheet", "description": "Create a blank worksheet with a title", "type": "MCP_Google_Sheets_create_worksheet", "inputs": [{"name": "spreadsheet_id", "display_name": "Spreadsheet Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "title", "display_name": "Title", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "row_count", "display_name": "Row Count", "info": "", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "column_count", "display_name": "Column Count", "info": "", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 26, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Google Sheets - create_worksheet\nDescription : Create a blank worksheet with a title\nType : MCP_Google_Sheets_create_worksheet\n", "input_info": "\nInputs : \n\n----------------------\n\nName : spreadsheet_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : title\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : row_count\nInfo : \nInput Type : int\nInput Types : ['int', 'Any']\nHandle\nDefault Value : 1000\n----------------------\n\nName : column_count\nInfo : \nInput Type : int\nInput Types : ['int', 'Any']\nHandle\nDefault Value : 26", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Google_Sheets_add_single_row": {"name": "Google Sheets - add_single_row", "description": "Add a single row of data to Google Sheets", "type": "MCP_Google_Sheets_add_single_row", "inputs": [{"name": "spreadsheet_id", "display_name": "Spreadsheet Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "worksheet_name", "display_name": "Worksheet Name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "row", "display_name": "Row", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "row_index", "display_name": "Row Index", "info": "", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Google Sheets - add_single_row\nDescription : Add a single row of data to Google Sheets\nType : MCP_Google_Sheets_add_single_row\n", "input_info": "\nInputs : \n\n----------------------\n\nName : spreadsheet_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : worksheet_name\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : row\nInfo : \nInput Type : array\nInput Types : ['array', 'Any']\nRequired\nHandle\nIs LIst\n----------------------\n\nName : row_index\nInfo : \nInput Type : int\nInput Types : ['int', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "IDGeneratorComponent": {"name": "ID Generator", "description": "Generates various types of unique identifiers (UUID, timestamp, short ID).", "type": "IDGeneratorComponent", "inputs": [{"name": "trigger", "display_name": "<PERSON><PERSON>", "info": "Optional input to control when ID generation occurs. Leave unconnected for immediate execution.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "id_type", "display_name": "ID Type", "info": "The type of unique identifier to generate.", "input_type": "dropdown", "input_types": ["dropdown"], "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "UUIDv4", "options": ["UUIDv4", "Timestamp ID", "Short ID"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "short_id_length", "display_name": "Short ID Length", "info": "The length of the short ID (only used when ID Type is 'Short ID').", "input_type": "int", "input_types": ["int"], "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 8, "options": null, "visibility_rules": [{"field_name": "id_type", "field_value": "Short ID", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "unique_id", "display_name": "Unique ID", "output_type": "string", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "node_info": "\nName : ID Generator\nDescription : Generates various types of unique identifiers (UUID, timestamp, short ID).\nType : IDGeneratorComponent\n", "input_info": "\nInputs : \n\n----------------------\n\nName : trigger\nInfo : Optional input to control when ID generation occurs. Leave unconnected for immediate execution.\nInput Type : handle\nInput Types : ['Any']\nHandle\n----------------------\n\nName : id_type\nInfo : The type of unique identifier to generate.\nInput Type : dropdown\nInput Types : ['dropdown']\nOptions : ['UUIDv4', 'Timestamp ID', 'Short ID']\nDefault Value : UUIDv4\n----------------------\n\nName : short_id_length\nInfo : The length of the short ID (only used when ID Type is 'Short ID').\nInput Type : int\nInput Types : ['int']\nDefault Value : 8", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : unique_id\ndisplay_name : Unique ID\noutput_type : string\n----------------------\n\nname : error\ndisplay_name : Error\noutput_type : str"}, "MCP_Google_Forms_create_google_form": {"name": "Google Forms - create_google_form", "description": "Create a new Google Form", "type": "MCP_Google_Forms_create_google_form", "inputs": [{"name": "title", "display_name": "Title", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "description", "display_name": "Description", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Google Forms - create_google_form\nDescription : Create a new Google Form\nType : MCP_Google_Forms_create_google_form\n", "input_info": "\nInputs : \n\n----------------------\n\nName : title\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : description\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Google_Forms_update_google_form": {"name": "Google Forms - update_google_form", "description": "Update a Google Form with new questions", "type": "MCP_Google_Forms_update_google_form", "inputs": [{"name": "form_id", "display_name": "Form Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "requests", "display_name": "Requests", "info": "A list of requests to update the form. See the Google Forms API documentation for more details.", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Google Forms - update_google_form\nDescription : Update a Google Form with new questions\nType : MCP_Google_Forms_update_google_form\n", "input_info": "\nInputs : \n\n----------------------\n\nName : form_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : requests\nInfo : A list of requests to update the form. See the Google Forms API documentation for more details.\nInput Type : array\nInput Types : ['array', 'Any']\nRequired\nHandle\nIs LIst", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Google_Document_update_document": {"name": "Google Document - update_document", "description": "Update a Google Document with new content at a specific position", "type": "MCP_Google_Document_update_document", "inputs": [{"name": "document_id", "display_name": "Document Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "content", "display_name": "Content", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "insert_at", "display_name": "Insert At", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Google Document - update_document\nDescription : Update a Google Document with new content at a specific position\nType : MCP_Google_Document_update_document\n", "input_info": "\nInputs : \n\n----------------------\n\nName : document_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : content\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : insert_at\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Google_Document_append_document": {"name": "Google Document - append_document", "description": "Append content to the end of a Google Document", "type": "MCP_Google_Document_append_document", "inputs": [{"name": "document_id", "display_name": "Document Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "content", "display_name": "Content", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "generated_string_output", "display_name": "generated_string_output", "output_type": "string"}], "node_info": "\nName : Google Document - append_document\nDescription : Append content to the end of a Google Document\nType : MCP_Google_Document_append_document\n", "input_info": "\nInputs : \n\n----------------------\n\nName : document_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : content\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : generated_string_output\ndisplay_name : generated_string_output\noutput_type : string"}, "MCP_Gmail_send_email": {"name": "Gmail - send_email", "description": "Create and send a new email message", "type": "MCP_Gmail_send_email", "inputs": [{"name": "to", "display_name": "To", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "subject", "display_name": "Subject", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "body", "display_name": "Body", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "cc", "display_name": "Cc", "info": "", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "bcc", "display_name": "Bcc", "info": "", "input_type": "array", "input_types": null, "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "html", "display_name": "Html", "info": "", "input_type": "bool", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "body", "display_name": "body", "output_type": "string"}], "node_info": "\nName : Gmail - send_email\nDescription : Create and send a new email message\nType : MCP_Gmail_send_email\n", "input_info": "\nInputs : \n\n----------------------\n\nName : to\nInfo : \nInput Type : string\nRequired\nHandle\n----------------------\n\nName : subject\nInfo : \nInput Type : string\nRequired\nHandle\n----------------------\n\nName : body\nInfo : \nInput Type : string\nRequired\nHandle\n----------------------\n\nName : cc\nInfo : \nInput Type : array\nHandle\nIs LIst\n----------------------\n\nName : bcc\nInfo : \nInput Type : array\nHandle\nIs LIst\n----------------------\n\nName : html\nInfo : \nInput Type : bool\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : body\ndisplay_name : body\noutput_type : string"}, "MCP_MetaAds_create_meta_campaign": {"name": "MetaAds - create_meta_campaign", "description": "Create a Meta Ads campaign with specified configuration", "type": "MCP_MetaAds_create_meta_campaign", "inputs": [{"name": "name", "display_name": "Name", "info": "Campaign name", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "objective", "display_name": "objective", "info": "Campaign objective", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "status", "display_name": "status", "info": "Campaign status", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "PAUSED", "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "buying_type", "display_name": "Buying Type", "info": "Buying type for the campaign", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "AUCTION", "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "bid_strategy", "display_name": "bid strategy", "info": "Bidding strategy", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "daily_budget", "display_name": "Daily Budget", "info": "Daily budget in cents", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "lifetime_budget", "display_name": "Lifetime Budget", "info": "Lifetime budget in cents", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "start_time", "display_name": "Start Time", "info": "Campaign start time (ISO format)", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "end_time", "display_name": "End Time", "info": "Campaign end time (ISO format)", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "special_ad_categories", "display_name": "Special Ad Categories", "info": "Special ad categories (required by Meta API)", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : MetaAds - create_meta_campaign\nDescription : Create a Meta Ads campaign with specified configuration\nType : MCP_MetaAds_create_meta_campaign\n", "input_info": "\nInputs : \n\n----------------------\n\nName : name\nInfo : Campaign name\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : objective\nInfo : Campaign objective\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : status\nInfo : Campaign status\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\nDefault Value : PAUSED\n----------------------\n\nName : buying_type\nInfo : Buying type for the campaign\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\nDefault Value : AUCTION\n----------------------\n\nName : bid_strategy\nInfo : Bidding strategy\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : daily_budget\nInfo : Daily budget in cents\nInput Type : int\nInput Types : ['int', 'Any']\nHandle\n----------------------\n\nName : lifetime_budget\nInfo : Lifetime budget in cents\nInput Type : int\nInput Types : ['int', 'Any']\nHandle\n----------------------\n\nName : start_time\nInfo : Campaign start time (ISO format)\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : end_time\nInfo : Campaign end time (ISO format)\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : special_ad_categories\nInfo : Special ad categories (required by Meta API)\nInput Type : array\nInput Types : ['array', 'Any']\nHandle\nIs LIst", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_MetaAds_create_adset": {"name": "MetaAds - create_adset", "description": "Create a new ad set in a Meta Ads account", "type": "MCP_MetaAds_create_adset", "inputs": [{"name": "campaign_id", "display_name": "Campaign Id", "info": "Meta Ads campaign ID this ad set belongs to", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "name", "display_name": "Name", "info": "Ad set name", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "status", "display_name": "Status", "info": "Initial ad set status", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "PAUSED", "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "daily_budget", "display_name": "Daily Budget", "info": "Daily budget in cents", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "lifetime_budget", "display_name": "Lifetime Budget", "info": "Lifetime budget in cents", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "countries", "display_name": "Countries", "info": "List of target countries (e.g., ['US', 'CA'])", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "publisher_platforms", "display_name": "Publisher Platforms", "info": "List of publisher platforms (e.g., ['facebook', 'instagram'])", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "facebook_positions", "display_name": "Facebook Positions", "info": "List of Facebook positions (e.g., ['feed', 'right_hand_column'])", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "optimization_goal", "display_name": "Optimization Goal", "info": "Conversion optimization goal (e.g., 'LINK_CLICKS')", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "billing_event", "display_name": "Billing Event", "info": "How you're charged (e.g., 'IMPRESSIONS')", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "bid_amount", "display_name": "<PERSON><PERSON>", "info": "Bid amount in account currency (in cents)", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "bid_strategy", "display_name": "Bid Strategy", "info": "Bid strategy (e.g., 'LOWEST_COST')", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "start_time", "display_name": "Start Time", "info": "Start time (ISO 8601)", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "end_time", "display_name": "End Time", "info": "End time (ISO 8601)", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : MetaAds - create_adset\nDescription : Create a new ad set in a Meta Ads account\nType : MCP_MetaAds_create_adset\n", "input_info": "\nInputs : \n\n----------------------\n\nName : campaign_id\nInfo : Meta Ads campaign ID this ad set belongs to\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : name\nInfo : Ad set name\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : status\nInfo : Initial ad set status\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\nDefault Value : PAUSED\n----------------------\n\nName : daily_budget\nInfo : Daily budget in cents\nInput Type : int\nInput Types : ['int', 'Any']\nHandle\n----------------------\n\nName : lifetime_budget\nInfo : Lifetime budget in cents\nInput Type : int\nInput Types : ['int', 'Any']\nHandle\n----------------------\n\nName : countries\nInfo : List of target countries (e.g., ['US', 'CA'])\nInput Type : array\nInput Types : ['array', 'Any']\nRequired\nHandle\nIs LIst\n----------------------\n\nName : publisher_platforms\nInfo : List of publisher platforms (e.g., ['facebook', 'instagram'])\nInput Type : array\nInput Types : ['array', 'Any']\nRequired\nHandle\nIs LIst\n----------------------\n\nName : facebook_positions\nInfo : List of Facebook positions (e.g., ['feed', 'right_hand_column'])\nInput Type : array\nInput Types : ['array', 'Any']\nRequired\nHandle\nIs LIst\n----------------------\n\nName : optimization_goal\nInfo : Conversion optimization goal (e.g., 'LINK_CLICKS')\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : billing_event\nInfo : How you're charged (e.g., 'IMPRESSIONS')\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : bid_amount\nInfo : Bid amount in account currency (in cents)\nInput Type : int\nInput Types : ['int', 'Any']\nHandle\n----------------------\n\nName : bid_strategy\nInfo : Bid strategy (e.g., 'LOWEST_COST')\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : start_time\nInfo : Start time (ISO 8601)\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : end_time\nInfo : End time (ISO 8601)\nInput Type : string\nInput Types : ['string', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_MetaAds_upload_ad_image": {"name": "MetaAds - upload_ad_image", "description": "Upload an image to use in Meta Ads creatives", "type": "MCP_MetaAds_upload_ad_image", "inputs": [{"name": "image_url", "display_name": "Image Url", "info": "Image file URL for upload", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : MetaAds - upload_ad_image\nDescription : Upload an image to use in Meta Ads creatives\nType : MCP_MetaAds_upload_ad_image\n", "input_info": "\nInputs : \n\n----------------------\n\nName : image_url\nInfo : Image file URL for upload\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_MetaAds_create_ad_creative": {"name": "MetaAds - create_ad_creative", "description": "Create a new ad creative using an uploaded image hash", "type": "MCP_MetaAds_create_ad_creative", "inputs": [{"name": "name", "display_name": "Name", "info": "Creative name", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "page_id", "display_name": "Page Id", "info": "Page ID for the ad creative", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "image_hash", "display_name": "Image Hash", "info": "Image hash for the ad creative", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "link", "display_name": "Link", "info": "Landing page URL for the ad creative", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "message", "display_name": "Message", "info": "Primary text for the ad creative", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "image_name", "display_name": "Image Name", "info": "Name of the image", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "description", "display_name": "Description", "info": "Description of the ad creative", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "call_to_action", "display_name": "Call To Action", "info": "Call to action for the ad creative", "input_type": "object", "input_types": ["object", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : MetaAds - create_ad_creative\nDescription : Create a new ad creative using an uploaded image hash\nType : MCP_MetaAds_create_ad_creative\n", "input_info": "\nInputs : \n\n----------------------\n\nName : name\nInfo : Creative name\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : page_id\nInfo : Page ID for the ad creative\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : image_hash\nInfo : Image hash for the ad creative\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : link\nInfo : Landing page URL for the ad creative\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : message\nInfo : Primary text for the ad creative\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : image_name\nInfo : Name of the image\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : description\nInfo : Description of the ad creative\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : call_to_action\nInfo : Call to action for the ad creative\nInput Type : object\nInput Types : ['object', 'Any']\nRequired\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_MetaAds_create_ad": {"name": "MetaAds - create_ad", "description": "Create a new ad with an existing creative", "type": "MCP_MetaAds_create_ad", "inputs": [{"name": "name", "display_name": "Name", "info": "Ad name", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "adset_id", "display_name": "Adset Id", "info": "Ad set ID where this ad will be placed", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "creative_id", "display_name": "Creative Id", "info": "Creative ID to be used for this ad", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "status", "display_name": "Status", "info": "Initial ad status", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "PAUSED", "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "bid_amount", "display_name": "<PERSON><PERSON>", "info": "Optional bid amount (in cents)", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "tracking_specs", "display_name": "Tracking Specs", "info": "Optional tracking specifications", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : MetaAds - create_ad\nDescription : Create a new ad with an existing creative\nType : MCP_MetaAds_create_ad\n", "input_info": "\nInputs : \n\n----------------------\n\nName : name\nInfo : Ad name\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : adset_id\nInfo : Ad set ID where this ad will be placed\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : creative_id\nInfo : Creative ID to be used for this ad\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : status\nInfo : Initial ad status\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\nDefault Value : PAUSED\n----------------------\n\nName : bid_amount\nInfo : Optional bid amount (in cents)\nInput Type : int\nInput Types : ['int', 'Any']\nHandle\n----------------------\n\nName : tracking_specs\nInfo : Optional tracking specifications\nInput Type : array\nInput Types : ['array', 'Any']\nHandle\nIs LIst", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "workflow-3c17973e-646f-48a8-b84c-eb65f37646b5": {"name": "Profile Research Workflow", "description": "Profile_Research_Workflow", "type": "workflow-3c17973e-646f-48a8-b84c-eb65f37646b5", "inputs": [{"name": "iteration_list", "display_name": "Iteration List", "info": "Input field: iteration_list", "input_type": "string", "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR", "transition_id": "transition-LoopNode-1752762417794"}], "outputs": [{"name": "execution_status", "display_name": "Execution Status", "output_type": "string"}, {"name": "workflow_execution_id", "display_name": "Execution ID", "output_type": "string"}, {"name": "message", "display_name": "Message", "output_type": "string"}], "node_info": "\nName : Profile Research Workflow\nDescription : Profile_Research_Workflow\nType : workflow-3c17973e-646f-48a8-b84c-eb65f37646b5\n", "input_info": "\nInputs : \n\n----------------------\n\nName : iteration_list\nInfo : Input field: iteration_list\nInput Type : string\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : execution_status\ndisplay_name : Execution Status\noutput_type : string\n----------------------\n\nname : workflow_execution_id\ndisplay_name : Execution ID\noutput_type : string\n----------------------\n\nname : message\ndisplay_name : Message\noutput_type : string"}, "workflow-d3ec861d-da29-4e5e-8c8e-db480d15d5cd": {"name": "Company Research Workflow", "description": "Company_Research_Workflow", "type": "workflow-d3ec861d-da29-4e5e-8c8e-db480d15d5cd", "inputs": [{"name": "query", "display_name": "Query", "info": "Input field: query", "input_type": "string", "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR", "transition_id": "transition-AgenticAI-1752768337944"}], "outputs": [{"name": "execution_status", "display_name": "Execution Status", "output_type": "string"}, {"name": "workflow_execution_id", "display_name": "Execution ID", "output_type": "string"}, {"name": "message", "display_name": "Message", "output_type": "string"}], "node_info": "\nName : Company Research Workflow\nDescription : Company_Research_Workflow\nType : workflow-d3ec861d-da29-4e5e-8c8e-db480d15d5cd\n", "input_info": "\nInputs : \n\n----------------------\n\nName : query\nInfo : Input field: query\nInput Type : string\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : execution_status\ndisplay_name : Execution Status\noutput_type : string\n----------------------\n\nname : workflow_execution_id\ndisplay_name : Execution ID\noutput_type : string\n----------------------\n\nname : message\ndisplay_name : Message\noutput_type : string"}, "workflow-cf07c857-008c-44a7-b164-07f40cee8461": {"name": "Research Workflow", "description": "Research_Workflow", "type": "workflow-cf07c857-008c-44a7-b164-07f40cee8461", "inputs": [{"name": "query", "display_name": "Query", "info": "Input field: query", "input_type": "string", "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR", "transition_id": "transition-AgenticAI-1752769698362"}], "outputs": [{"name": "execution_status", "display_name": "Execution Status", "output_type": "string"}, {"name": "workflow_execution_id", "display_name": "Execution ID", "output_type": "string"}, {"name": "message", "display_name": "Message", "output_type": "string"}], "node_info": "\nName : Research Workflow\nDescription : Research_Workflow\nType : workflow-cf07c857-008c-44a7-b164-07f40cee8461\n", "input_info": "\nInputs : \n\n----------------------\n\nName : query\nInfo : Input field: query\nInput Type : string\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : execution_status\ndisplay_name : Execution Status\noutput_type : string\n----------------------\n\nname : workflow_execution_id\ndisplay_name : Execution ID\noutput_type : string\n----------------------\n\nname : message\ndisplay_name : Message\noutput_type : string"}, "MCP_Leonardo_MCP_generateHeroImage": {"name": "Leonardo MCP - generateHeroImage", "description": "Generate a hero image from text", "type": "MCP_Leonardo_MCP_generateHeroImage", "inputs": [{"name": "prompt", "display_name": "prompt", "info": "Text to generate image from", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : <PERSON> MCP - generateHeroImage\nDescription : Generate a hero image from text\nType : MCP_Leonardo_MCP_generateHeroImage\n", "input_info": "\nInputs : \n\n----------------------\n\nName : prompt\nInfo : Text to generate image from\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Redis-MCP_hset": {"name": "Redis-MCP - hset", "description": "Set a field in a hash stored at key with an optional expiration time.\n\nArgs:\n    name: The Redis hash key.\n    key: The field name inside the hash.\n    value: The value to set.\n    expire_seconds: Optional; time in seconds after which the key should expire.\n\nReturns:\n    A success message or an error message.\n", "type": "MCP_Redis-MCP_hset", "inputs": [{"name": "name", "display_name": "Name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "key", "display_name": "Key", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "value", "display_name": "Value", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "expire_seconds", "display_name": "Expire Seconds", "info": "", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Redis-MCP - hset\nDescription : Set a field in a hash stored at key with an optional expiration time.\n\nArgs:\n    name: The Redis hash key.\n    key: The field name inside the hash.\n    value: The value to set.\n    expire_seconds: Optional; time in seconds after which the key should expire.\n\nReturns:\n    A success message or an error message.\n\nType : MCP_Redis-MCP_hset\n", "input_info": "\nInputs : \n\n----------------------\n\nName : name\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : key\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : value\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : expire_seconds\nInfo : \nInput Type : int\nInput Types : ['int', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Redis-MCP_hget": {"name": "Redis-MCP - hget", "description": "Get the value of a field in a Redis hash.\n\nArgs:\n    name: The Redis hash key.\n    key: The field name inside the hash.\n\nReturns:\n    The field value or an error message.\n", "type": "MCP_Redis-MCP_hget", "inputs": [{"name": "name", "display_name": "Name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "key", "display_name": "Key", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Redis-MCP - hget\nDescription : Get the value of a field in a Redis hash.\n\nArgs:\n    name: The Redis hash key.\n    key: The field name inside the hash.\n\nReturns:\n    The field value or an error message.\n\nType : MCP_Redis-MCP_hget\n", "input_info": "\nInputs : \n\n----------------------\n\nName : name\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : key\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Redis-MCP_hgetall": {"name": "Redis-MCP - hgetall", "description": "Get all fields and values from a Redis hash.\n\nArgs:\n    name: The Redis hash key.\n\nReturns:\n    A dictionary of field-value pairs or an error message.\n", "type": "MC<PERSON>_<PERSON><PERSON>-MC<PERSON>_h<PERSON>all", "inputs": [{"name": "name", "display_name": "Name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Redis-MCP - hgetall\nDescription : Get all fields and values from a Redis hash.\n\nArgs:\n    name: The Redis hash key.\n\nReturns:\n    A dictionary of field-value pairs or an error message.\n\nType : MCP_Redis-MCP_hgetall\n", "input_info": "\nInputs : \n\n----------------------\n\nName : name\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\n<PERSON><PERSON>", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_CMS-MCP_create_webflow_blog_post": {"name": "CMS-MCP - create_webflow_blog_post", "description": "Create a blog post in Webflow. This tool automatically analyzes the blog post content, creates the necessary collection fields if they don't exist, and posts the blog content. No need to specify collection fields - they are generated automatically based on the blog post data.", "type": "MCP_CMS-MCP_create_webflow_blog_post", "inputs": [{"name": "webflowToken", "display_name": "webflowToken", "info": "Webflow API token", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "siteId", "display_name": "siteId", "info": "Webflow site ID", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "collectionName", "display_name": "collectionName", "info": "Name of the blog collection", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "blogPost", "display_name": "blogPost", "info": "Blog post data", "input_type": "object", "input_types": ["object", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "publishAfterCreation", "display_name": "publishAfterCreation", "info": "Whether to publish the site after creating the post", "input_type": "bool", "input_types": ["bool", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "customDomains", "display_name": "customDomains", "info": "Custom domains to publish to", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : CMS-MCP - create_webflow_blog_post\nDescription : Create a blog post in Webflow. This tool automatically analyzes the blog post content, creates the necessary collection fields if they don't exist, and posts the blog content. No need to specify collection fields - they are generated automatically based on the blog post data.\nType : MCP_CMS-MCP_create_webflow_blog_post\n", "input_info": "\nInputs : \n\n----------------------\n\nName : webflowToken\nInfo : Webflow API token\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : siteId\nInfo : Webflow site ID\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : collectionName\nInfo : Name of the blog collection\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : blogPost\nInfo : Blog post data\nInput Type : object\nInput Types : ['object', 'Any']\nRequired\nHandle\n----------------------\n\nName : publishAfterCreation\nInfo : Whether to publish the site after creating the post\nInput Type : bool\nInput Types : ['bool', 'Any']\nHandle\n----------------------\n\nName : customDomains\nInfo : Custom domains to publish to\nInput Type : array\nInput Types : ['array', 'Any']\nHandle\nIs LIst", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Redis-MCP_set": {"name": "Redis-MCP - set", "description": "Set a Redis string value with an optional expiration time.\n\nArgs:\n    key (str): The key to set.\n    value (str): The value to store.\n    expiration (int, optional): Expiration time in seconds.\n\nReturns:\n    str: Confirmation message or an error message.\n", "type": "MCP_Redis-MCP_set", "inputs": [{"name": "key", "display_name": "Key", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "value", "display_name": "Value", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "expiration", "display_name": "Expiration", "info": "", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Redis-MCP - set\nDescription : Set a Redis string value with an optional expiration time.\n\nArgs:\n    key (str): The key to set.\n    value (str): The value to store.\n    expiration (int, optional): Expiration time in seconds.\n\nReturns:\n    str: Confirmation message or an error message.\n\nType : MCP_Redis-MCP_set\n", "input_info": "\nInputs : \n\n----------------------\n\nName : key\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : value\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : expiration\nInfo : \nInput Type : int\nInput Types : ['int', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Redis-MCP_get": {"name": "Redis-MCP - get", "description": "Get a Redis string value.\n\nArgs:\n    key (str): The key to retrieve.\n\nReturns:\n    str: The stored value or an error message.\n", "type": "MCP_Redis-MCP_get", "inputs": [{"name": "key", "display_name": "Key", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Redis-MCP - get\nDescription : Get a Redis string value.\n\nArgs:\n    key (str): The key to retrieve.\n\nReturns:\n    str: The stored value or an error message.\n\nType : MCP_Redis-MCP_get\n", "input_info": "\nInputs : \n\n----------------------\n\nName : key\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\n<PERSON><PERSON>", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Google_Sheets_get_values_in_range": {"name": "Google Sheets - get_values_in_range", "description": "Get all values or values from a range of cells using A1 notation", "type": "MCP_Google_Sheets_get_values_in_range", "inputs": [{"name": "spreadsheet_id", "display_name": "Spreadsheet Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "range_notation", "display_name": "Range Notation", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "value_render_option", "display_name": "Value Render Option", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "FORMATTED_VALUE", "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Google Sheets - get_values_in_range\nDescription : Get all values or values from a range of cells using A1 notation\nType : MCP_Google_Sheets_get_values_in_range\n", "input_info": "\nInputs : \n\n----------------------\n\nName : spreadsheet_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : range_notation\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : value_render_option\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\nDefault Value : FORMATTED_VALUE", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Google_Forms_get_google_form_responses": {"name": "Google Forms - get_google_form_responses", "description": "Get responses for a Google Form", "type": "MCP_Google_Forms_get_google_form_responses", "inputs": [{"name": "form_id", "display_name": "Form Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Google Forms - get_google_form_responses\nDescription : Get responses for a Google Form\nType : MCP_Google_Forms_get_google_form_responses\n", "input_info": "\nInputs : \n\n----------------------\n\nName : form_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\n<PERSON><PERSON>", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_PDF_Reader_extract_metadata": {"name": "PDF Reader - extract_metadata", "description": "Extract metadata information from a file by URL including source, file_url, file_name, format, size, and other file properties", "type": "MCP_PDF_Reader_extract_metadata", "inputs": [{"name": "file_url", "display_name": "file url", "info": "Public URL of the file to extract metadata from", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "success", "display_name": "success", "output_type": "boolean"}, {"name": "source", "display_name": "source", "output_type": "string"}, {"name": "file_url", "display_name": "file url", "output_type": "string"}, {"name": "file_name", "display_name": "file name", "output_type": "string"}, {"name": "format", "display_name": "format", "output_type": "string"}, {"name": "file_info", "display_name": "file info", "output_type": "object"}], "node_info": "\nName : PDF Reader - extract_metadata\nDescription : Extract metadata information from a file by URL including source, file_url, file_name, format, size, and other file properties\nType : MCP_PDF_Reader_extract_metadata\n", "input_info": "\nInputs : \n\n----------------------\n\nName : file_url\nInfo : Public URL of the file to extract metadata from\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : success\ndisplay_name : success\noutput_type : boolean\n----------------------\n\nname : source\ndisplay_name : source\noutput_type : string\n----------------------\n\nname : file_url\ndisplay_name : file url\noutput_type : string\n----------------------\n\nname : file_name\ndisplay_name : file name\noutput_type : string\n----------------------\n\nname : format\ndisplay_name : format\noutput_type : string\n----------------------\n\nname : file_info\ndisplay_name : file info\noutput_type : object"}, "MCP_PDF_Reader_extract_file_content": {"name": "PDF Reader - extract_file_content", "description": "Extract content from PDF, DOC, DOCX, CSV, or XLSX files with optional pagination and search for spreadsheets", "type": "MCP_PDF_Reader_extract_file_content", "inputs": [{"name": "source", "display_name": "source", "info": "Source type: URL or local upload", "input_type": "dropdown", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": ["url", "upload"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "file_url", "display_name": "file url", "info": "Public URL of the file (required if source is 'url')", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "file_data", "display_name": "file data", "info": "Base64 encoded file data (required if source is 'upload')", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "file_name", "display_name": "file name", "info": "Original filename with extension", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "format", "display_name": "format", "info": "File format", "input_type": "dropdown", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": ["pdf", "doc", "docx", "csv", "xlsx", "pptx", "xls", "ppt"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "limit", "display_name": "limit", "info": "Number of rows to return (for CSV/XLSX only, ignored for PDF/DOC)", "input_type": "number", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 100, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "offset", "display_name": "offset", "info": "Number of rows to skip (for CSV/XLSX only, ignored for PDF/DOC)", "input_type": "number", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "sheet_name", "display_name": "sheet name", "info": "Specific sheet name for XLSX files (optional, defaults to first sheet)", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "search_query", "display_name": "search query", "info": "Search query to filter rows in CSV/XLSX files (only applicable for spreadsheet formats)", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "search_type", "display_name": "search type", "info": "Type of search to perform in CSV/XLSX files (default: 'full_text')", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "full_text", "options": ["full_text", "exact_match", "regex"], "visibility_rules": null, "visibility_logic": "OR"}, {"name": "case_sensitive", "display_name": "case sensitive", "info": "Whether search should be case sensitive for CSV/XLSX files (default: false)", "input_type": "bool", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "data", "display_name": "data", "output_type": "string"}], "node_info": "\nName : PDF Reader - extract_file_content\nDescription : Extract content from PDF, DOC, DOCX, CSV, or XLSX files with optional pagination and search for spreadsheets\nType : MCP_PDF_Reader_extract_file_content\n", "input_info": "\nInputs : \n\n----------------------\n\nName : source\nInfo : Source type: URL or local upload\nInput Type : dropdown\nRequired\nHandle\nOptions : ['url', 'upload']\n----------------------\n\nName : file_url\nInfo : Public URL of the file (required if source is 'url')\nInput Type : string\nHandle\n----------------------\n\nName : file_data\nInfo : Base64 encoded file data (required if source is 'upload')\nInput Type : string\nHandle\n----------------------\n\nName : file_name\nInfo : Original filename with extension\nInput Type : string\nRequired\nHandle\n----------------------\n\nName : format\nInfo : File format\nInput Type : dropdown\nRequired\nHandle\nOptions : ['pdf', 'doc', 'docx', 'csv', 'xlsx', 'pptx', 'xls', 'ppt']\n----------------------\n\nName : limit\nInfo : Number of rows to return (for CSV/XLSX only, ignored for PDF/DOC)\nInput Type : number\nHandle\nDefault Value : 100\n----------------------\n\nName : offset\nInfo : Number of rows to skip (for CSV/XLSX only, ignored for PDF/DOC)\nInput Type : number\nHandle\n----------------------\n\nName : sheet_name\nInfo : Specific sheet name for XLSX files (optional, defaults to first sheet)\nInput Type : string\nHandle\n----------------------\n\nName : search_query\nInfo : Search query to filter rows in CSV/XLSX files (only applicable for spreadsheet formats)\nInput Type : string\nHandle\n----------------------\n\nName : search_type\nInfo : Type of search to perform in CSV/XLSX files (default: 'full_text')\nInput Type : dropdown\nHandle\nOptions : ['full_text', 'exact_match', 'regex']\nDefault Value : full_text\n----------------------\n\nName : case_sensitive\nInfo : Whether search should be case sensitive for CSV/XLSX files (default: false)\nInput Type : bool\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : data\ndisplay_name : data\noutput_type : string"}, "MCP_Redis_MCP_delete": {"name": "Redis MCP - delete", "description": "Delete a Redis key.\n\nArgs:\n    key (str): The key to delete.\n\nReturns:\n    str: Confirmation message or an error message.\n", "type": "MCP_Redis_MCP_delete", "inputs": [{"name": "key", "display_name": "Key", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Redis MCP - delete\nDescription : Delete a Redis key.\n\nArgs:\n    key (str): The key to delete.\n\nReturns:\n    str: Confirmation message or an error message.\n\nType : MCP_Redis_MCP_delete\n", "input_info": "\nInputs : \n\n----------------------\n\nName : key\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\n<PERSON><PERSON>", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "SplitTextComponent": {"name": "Split Text", "description": "Splits text into a list using a delimiter.", "type": "SplitTextComponent", "inputs": [{"name": "input_text", "display_name": "Input Text", "info": "The text to split. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "delimiter", "display_name": "Delimiter", "info": "The character or string to split the text by.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": ",", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_splits", "display_name": "Max Splits", "info": "Maximum number of splits to perform. -1 means no limit.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": -1, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "include_delimiter", "display_name": "Include Delimiter", "info": "If enabled, the delimiter will be included at the end of each split part (except the last one).", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": false, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "output_list", "display_name": "Split List", "output_type": "list", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "node_info": "\nName : Split Text\nDescription : Splits text into a list using a delimiter.\nType : SplitTextComponent\n", "input_info": "\nInputs : \n\n----------------------\n\nName : input_text\nInfo : The text to split. Can be connected from another node or entered directly.\nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : delimiter\nInfo : The character or string to split the text by.\nInput Type : string\nDefault Value : ,\n----------------------\n\nName : max_splits\nInfo : Maximum number of splits to perform. -1 means no limit.\nInput Type : int\nAdvanced\nDefault Value : -1\n----------------------\n\nName : include_delimiter\nInfo : If enabled, the delimiter will be included at the end of each split part (except the last one).\nInput Type : bool\nAdvanced", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : output_list\ndisplay_name : Split List\noutput_type : list\n----------------------\n\nname : error\ndisplay_name : Error\noutput_type : str"}, "workflow-e840128c-5017-486a-9374-da5428dc0e6d": {"name": "CREATE Document -- Workflow (Blog Gen)", "description": "CREATE_Document_--_Workflow_(Blog_Gen)", "type": "workflow-e840128c-5017-486a-9374-da5428dc0e6d", "inputs": [{"name": "input_data", "display_name": "Input Data", "info": "Input field: input_data", "input_type": "string", "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR", "transition_id": "transition-UniversalConverterComponent-1753336184655"}], "outputs": [{"name": "execution_status", "display_name": "Execution Status", "output_type": "string"}, {"name": "workflow_execution_id", "display_name": "Execution ID", "output_type": "string"}, {"name": "message", "display_name": "Message", "output_type": "string"}], "node_info": "\nName : CREATE Document -- Workflow (Blog Gen)\nDescription : CREATE_Document_--_Workflow_(Blog_Gen)\nType : workflow-e840128c-5017-486a-9374-da5428dc0e6d\n", "input_info": "\nInputs : \n\n----------------------\n\nName : input_data\nInfo : Input field: input_data\nInput Type : string\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : execution_status\ndisplay_name : Execution Status\noutput_type : string\n----------------------\n\nname : workflow_execution_id\ndisplay_name : Execution ID\noutput_type : string\n----------------------\n\nname : message\ndisplay_name : Message\noutput_type : string"}, "MCP_Google_Sheets_count_column_values": {"name": "Google Sheets - count_column_values", "description": "Count the total number of values in a specific column", "type": "MCP_Google_Sheets_count_column_values", "inputs": [{"name": "spreadsheet_id", "display_name": "Spreadsheet Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "worksheet_name", "display_name": "Worksheet Name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "column", "display_name": "Column", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "value", "display_name": "Value", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "include_empty", "display_name": "Include Empty", "info": "", "input_type": "bool", "input_types": ["bool", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Google Sheets - count_column_values\nDescription : Count the total number of values in a specific column\nType : MCP_Google_Sheets_count_column_values\n", "input_info": "\nInputs : \n\n----------------------\n\nName : spreadsheet_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : worksheet_name\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : column\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : value\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : include_empty\nInfo : \nInput Type : bool\nInput Types : ['bool', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Google_Sheets_set_formula": {"name": "Google Sheets - set_formula", "description": "Set a formula in a specific cell of a Google Sheet", "type": "MCP_Google_Sheets_set_formula", "inputs": [{"name": "spreadsheet_id", "display_name": "Spreadsheet Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "worksheet_name", "display_name": "Worksheet Name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "cell", "display_name": "Cell", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "formula", "display_name": "Formula", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Google Sheets - set_formula\nDescription : Set a formula in a specific cell of a Google Sheet\nType : MCP_Google_Sheets_set_formula\n", "input_info": "\nInputs : \n\n----------------------\n\nName : spreadsheet_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : worksheet_name\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : cell\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : formula\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Jira_&_Confluence_get_issue": {"name": "Jira & Confluence - get_issue", "description": "Get a Jira issue by key.", "type": "MCP_Jira_&_Confluence_get_issue", "inputs": [{"name": "issue<PERSON><PERSON>", "display_name": "issue<PERSON><PERSON>", "info": "Jira issue key (e.g., 'TEST-1')", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Jira & Confluence - get_issue\nDescription : Get a Jira issue by key.\nType : MCP_Jira_&_Confluence_get_issue\n", "input_info": "\nInputs : \n\n----------------------\n\nName : issueKey\nInfo : Jira issue key (e.g., 'TEST-1')\nInput Type : string\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Google_Sheets_update_row": {"name": "Google Sheets - update_row", "description": "Update a row in a spreadsheet", "type": "MCP_Google_Sheets_update_row", "inputs": [{"name": "spreadsheet_id", "display_name": "Spreadsheet Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "worksheet_name", "display_name": "Worksheet Name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "row_index", "display_name": "Row Index", "info": "", "input_type": "int", "input_types": ["int", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "values", "display_name": "Values", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Google Sheets - update_row\nDescription : Update a row in a spreadsheet\nType : MCP_Google_Sheets_update_row\n", "input_info": "\nInputs : \n\n----------------------\n\nName : spreadsheet_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : worksheet_name\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : row_index\nInfo : \nInput Type : int\nInput Types : ['int', 'Any']\nRequired\nHandle\n----------------------\n\nName : values\nInfo : \nInput Type : array\nInput Types : ['array', 'Any']\nRequired\nHandle\nIs LIst", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Google_Document_get_document": {"name": "Google Document - get_document", "description": "Retrieve the content of a Google Document by its ID", "type": "MCP_Google_Document_get_document", "inputs": [{"name": "document_id", "display_name": "Document Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Google Document - get_document\nDescription : Retrieve the content of a Google Document by its ID\nType : MCP_Google_Document_get_document\n", "input_info": "\nInputs : \n\n----------------------\n\nName : document_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\n<PERSON><PERSON>", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Gmail_create_draft": {"name": "Gmail - create_draft", "description": "Create a draft email message", "type": "MCP_Gmail_create_draft", "inputs": [{"name": "to", "display_name": "To", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "subject", "display_name": "Subject", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "body", "display_name": "Body", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "cc", "display_name": "Cc", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "bcc", "display_name": "Bcc", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "html", "display_name": "Html", "info": "", "input_type": "bool", "input_types": ["bool", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Gmail - create_draft\nDescription : Create a draft email message\nType : MCP_Gmail_create_draft\n", "input_info": "\nInputs : \n\n----------------------\n\nName : to\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : subject\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : body\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : cc\nInfo : \nInput Type : array\nInput Types : ['array', 'Any']\nHandle\nIs LIst\n----------------------\n\nName : bcc\nInfo : \nInput Type : array\nInput Types : ['array', 'Any']\nHandle\nIs LIst\n----------------------\n\nName : html\nInfo : \nInput Type : bool\nInput Types : ['bool', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_SDR_Management_get_campaign": {"name": "SDR Management - get_campaign", "description": "Get campaign details by ID", "type": "MCP_SDR_Management_get_campaign", "inputs": [{"name": "campaign_id", "display_name": "Campaign Id", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "user_id", "display_name": "User Id", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : SDR Management - get_campaign\nDescription : Get campaign details by ID\nType : MCP_SDR_Management_get_campaign\n", "input_info": "\nInputs : \n\n----------------------\n\nName : campaign_id\nInfo : \nInput Type : string\nRequired\nHandle\n----------------------\n\nName : user_id\nInfo : \nInput Type : string\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_SDR_Management_create_customers": {"name": "SDR Management - create_customers", "description": "Bulk create customers for a campaign from a list of Apollo person objects", "type": "MCP_SDR_Management_create_customers", "inputs": [{"name": "customers", "display_name": "Customers", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": true, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "user_id", "display_name": "User Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "campaign_id", "display_name": "Campaign Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "tags", "display_name": "Tags", "info": "", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : SDR Management - create_customers\nDescription : Bulk create customers for a campaign from a list of Apollo person objects\nType : MCP_SDR_Management_create_customers\n", "input_info": "\nInputs : \n\n----------------------\n\nName : customers\nInfo : \nInput Type : array\nInput Types : ['array', 'Any']\nRequired\nHandle\nIs LIst\n----------------------\n\nName : user_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : campaign_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : tags\nInfo : \nInput Type : array\nInput Types : ['array', 'Any']\nHandle\nIs LIst", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Apollo_IO_people_enrichment": {"name": "Apollo IO - people_enrichment", "description": "Use the People Enrichment endpoint to enrich data for 1 person", "type": "MCP_Apollo_IO_people_enrichment", "inputs": [{"name": "first_name", "display_name": "first name", "info": "Person's first name", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "last_name", "display_name": "last name", "info": "Person's last name", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "email", "display_name": "email", "info": "Person's email address", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "domain", "display_name": "domain", "info": "Company domain", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "organization_name", "display_name": "organization name", "info": "Organization name", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "linkedin_url", "display_name": "linkedin url", "info": "Person's LinkedIn profile URL", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Apollo IO - people_enrichment\nDescription : Use the People Enrichment endpoint to enrich data for 1 person\nType : MCP_Apollo_IO_people_enrichment\n", "input_info": "\nInputs : \n\n----------------------\n\nName : first_name\nInfo : Person's first name\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : last_name\nInfo : Person's last name\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : email\nInfo : Person's email address\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : domain\nInfo : Company domain\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : organization_name\nInfo : Organization name\nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : linkedin_url\nInfo : Person's LinkedIn profile URL\nInput Type : string\nInput Types : ['string', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_Apollo_IO_people_search": {"name": "Apollo IO - people_search", "description": "Use the People Search endpoint to find people", "type": "MCP_Apollo_IO_people_search", "inputs": [{"name": "q_organization_domains_list", "display_name": "q organization domains list", "info": "List of organization domains to search within", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "person_titles", "display_name": "person titles", "info": "List of job titles to search for", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "person_seniorities", "display_name": "person seniorities", "info": "List of seniority levels to search for", "input_type": "array", "input_types": ["array", "Any"], "required": false, "is_handle": true, "is_list": true, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : Apollo IO - people_search\nDescription : Use the People Search endpoint to find people\nType : MCP_Apollo_IO_people_search\n", "input_info": "\nInputs : \n\n----------------------\n\nName : q_organization_domains_list\nInfo : List of organization domains to search within\nInput Type : array\nInput Types : ['array', 'Any']\nHandle\nIs LIst\n----------------------\n\nName : person_titles\nInfo : List of job titles to search for\nInput Type : array\nInput Types : ['array', 'Any']\nHandle\nIs LIst\n----------------------\n\nName : person_seniorities\nInfo : List of seniority levels to search for\nInput Type : array\nInput Types : ['array', 'Any']\nHandle\nIs LIst", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_SDR_Management_list_products": {"name": "SDR Management - list_products", "description": "List products for a user (optionally filter by campaign_id)", "type": "MCP_SDR_Management_list_products", "inputs": [{"name": "user_id", "display_name": "User Id", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "campaign_id", "display_name": "Campaign Id", "info": "", "input_type": "string", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "limit", "display_name": "Limit", "info": "", "input_type": "int", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "offset", "display_name": "Offset", "info": "", "input_type": "int", "input_types": null, "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : SDR Management - list_products\nDescription : List products for a user (optionally filter by campaign_id)\nType : MCP_SDR_Management_list_products\n", "input_info": "\nInputs : \n\n----------------------\n\nName : user_id\nInfo : \nInput Type : string\nRequired\nHandle\n----------------------\n\nName : campaign_id\nInfo : \nInput Type : string\nHandle\n----------------------\n\nName : limit\nInfo : \nInput Type : int\nHandle\n----------------------\n\nName : offset\nInfo : \nInput Type : int\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_SDR_Management_create_email_conversation": {"name": "SDR Management - create_email_conversation", "description": "Create a new email conversation between customer and user", "type": "MCP_SDR_Management_create_email_conversation", "inputs": [{"name": "user_id", "display_name": "User Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "campaign_id", "display_name": "Campaign Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "customer_id", "display_name": "Customer Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "subject", "display_name": "Subject", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "body", "display_name": "Body", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "sender_name", "display_name": "Sender Name", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : SDR Management - create_email_conversation\nDescription : Create a new email conversation between customer and user\nType : MCP_SDR_Management_create_email_conversation\n", "input_info": "\nInputs : \n\n----------------------\n\nName : user_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : campaign_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : customer_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : subject\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : body\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : sender_name\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_SDR_Management_get_email_conversations": {"name": "SDR Management - get_email_conversations", "description": "Get email conversations with optional filtering", "type": "MCP_SDR_Management_get_email_conversations", "inputs": [{"name": "user_id", "display_name": "User Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "campaign_id", "display_name": "Campaign Id", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "customer_email", "display_name": "Customer <PERSON><PERSON>", "info": "", "input_type": "string", "input_types": ["string", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "is_active", "display_name": "Is Active", "info": "", "input_type": "bool", "input_types": ["bool", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "limit", "display_name": "Limit", "info": "", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}, {"name": "offset", "display_name": "Offset", "info": "", "input_type": "int", "input_types": ["int", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {}}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : SDR Management - get_email_conversations\nDescription : Get email conversations with optional filtering\nType : MCP_SDR_Management_get_email_conversations\n", "input_info": "\nInputs : \n\n----------------------\n\nName : user_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nRequired\nHandle\n----------------------\n\nName : campaign_id\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : customer_email\nInfo : \nInput Type : string\nInput Types : ['string', 'Any']\nHandle\n----------------------\n\nName : is_active\nInfo : \nInput Type : bool\nInput Types : ['bool', 'Any']\nHandle\n----------------------\n\nName : limit\nInfo : \nInput Type : int\nInput Types : ['int', 'Any']\nHandle\n----------------------\n\nName : offset\nInfo : \nInput Type : int\nInput Types : ['int', 'Any']\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_SDR_Management_reply_email_from_customer": {"name": "SDR Management - reply_email_from_customer", "description": "Create an email conversation record from a customer's reply using from/to emails (resolves user_id, customer_id, latest campaign)", "type": "MCP_SDR_Management_reply_email_from_customer", "inputs": [{"name": "from_email_address", "display_name": "From Email Address", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "to_email_address", "display_name": "To Email Address", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "subject", "display_name": "Subject", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "body", "display_name": "Body", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : SDR Management - reply_email_from_customer\nDescription : Create an email conversation record from a customer's reply using from/to emails (resolves user_id, customer_id, latest campaign)\nType : MCP_SDR_Management_reply_email_from_customer\n", "input_info": "\nInputs : \n\n----------------------\n\nName : from_email_address\nInfo : \nInput Type : string\nRequired\nHandle\n----------------------\n\nName : to_email_address\nInfo : \nInput Type : string\nRequired\nHandle\n----------------------\n\nName : subject\nInfo : \nInput Type : string\nRequired\nHandle\n----------------------\n\nName : body\nInfo : \nInput Type : string\nRequired\nHandle", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}, "MCP_SDR_Management_fetch_customer": {"name": "SDR Management - fetch_customer", "description": "Fetch customer details by customer_id and user_id", "type": "MCP_SDR_Management_fetch_customer", "inputs": [{"name": "customer_id", "display_name": "Customer Id", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}, {"name": "user_id", "display_name": "User Id", "info": "", "input_type": "string", "input_types": null, "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR"}], "outputs": [{"name": "result", "display_name": "Result", "output_type": "any"}], "node_info": "\nName : SDR Management - fetch_customer\nDescription : Fetch customer details by customer_id and user_id\nType : MCP_SDR_Management_fetch_customer\n", "input_info": "\nInputs : \n\n----------------------\n\nName : customer_id\nInfo : \nInput Type : string\nRequired\nHandle\n----------------------\n\nName : user_id\nInfo : \nInput Type : string\nRequired\n<PERSON>le", "output_info": "\nOutput Handles: \n\n----------------------\n\nname : result\ndisplay_name : Result\noutput_type : any"}}