{"0": "StartNode", "1": "MCP_Script_Generation_script_generate", "2": "MCP_Voice_generation_generate_audio", "3": "MCP_Voice_generation_fetch_audio", "4": "MCP_Candidate_Interview_candidate_suitability_pre", "5": "ApiRequestNode", "6": "AgenticAI", "7": "MCP_Candidate_Interview_generate_interview_agenda", "8": "CombineTextComponent", "9": "MCP_content-extractor-mcp_generate_subtitle", "10": "MCP_Stock_Video_Generation_generate_stock_video", "11": "MCP_video-generation-mcp_generate_video", "12": "MCP_Stock_Image_Generation_generate_ai_stock_image", "13": "MCP_voice-generation-mcp_generate_audio", "14": "MCP_voice-generation-mcp_fetch_audio", "15": "MCP_script-generation-mcp-server_generate_script", "16": "DelayComponent", "17": "SelectDataComponent", "18": "LoopNode", "19": "UniversalConverterComponent", "20": "MCP_cinematic-video-generator_generate_video", "21": "ConditionalNode", "22": "MCP_stock-image-generation-mcp_generate_ai_stock_image", "23": "MCP_script-generation-mcp_script_generate", "24": "MCP_DuckDuckGo_fetch_content", "25": "MCP_context-engine-mcp_search", "26": "MCP_SlideSpeak_generate_powerpoint_slide_by_slide", "27": "MergeDataComponent", "28": "MCP_Leonardo_AI_Image_Generator_generateHeroImage", "29": "AlterMetadataComponent", "30": "MCP_Redis-mcp-01_set", "31": "MCP_Redis-mcp-01_get", "32": "MCP_Eraser_Diagram_Generator_generateDiagram", "33": "MCP_Content_Management_System_create_webflow_blog_post", "34": "MCP_Website_Generator_repo_setup", "35": "MCP_<PERSON>ly_Web_Search_and_Extraction_Server_tavily-extract", "36": "MCP_Website_Generator_create_file", "37": "MCP_Website_Generator_push_changes", "38": "MCP_Website_Generator_list_files", "39": "MCP_Website_Generator_read_file", "40": "MCP_Google_Sheets_find_row", "41": "MCP_Google_Sheets_update_cell", "42": "MCP_Google_Sheets_get_cell", "43": "MCP_Google_Document_create_document", "44": "MCP_Google_Sheets_create_worksheet", "45": "MCP_Google_Sheets_add_single_row", "46": "IDGeneratorComponent", "47": "MCP_Google_Forms_create_google_form", "48": "MCP_Google_Forms_update_google_form", "49": "MCP_Google_Document_update_document", "50": "MCP_Google_Document_append_document", "51": "MCP_Gmail_send_email", "52": "MCP_MetaAds_create_meta_campaign", "53": "MCP_MetaAds_create_adset", "54": "MCP_MetaAds_upload_ad_image", "55": "MCP_MetaAds_create_ad_creative", "56": "MCP_MetaAds_create_ad", "57": "workflow-3c17973e-646f-48a8-b84c-eb65f37646b5", "58": "workflow-d3ec861d-da29-4e5e-8c8e-db480d15d5cd", "59": "workflow-cf07c857-008c-44a7-b164-07f40cee8461", "60": "MCP_Leonardo_MCP_generateHeroImage", "61": "MCP_Redis-MCP_hset", "62": "MCP_Redis-MCP_hget", "63": "MC<PERSON>_<PERSON><PERSON>-MC<PERSON>_h<PERSON>all", "64": "MCP_CMS-MCP_create_webflow_blog_post", "65": "MCP_Redis-MCP_set", "66": "MCP_Redis-MCP_get", "67": "MCP_Google_Sheets_get_values_in_range", "68": "MCP_Google_Forms_get_google_form_responses", "69": "MCP_PDF_Reader_extract_metadata", "70": "MCP_PDF_Reader_extract_file_content", "71": "MCP_Redis_MCP_delete", "72": "SplitTextComponent", "73": "workflow-e840128c-5017-486a-9374-da5428dc0e6d", "74": "MCP_Google_Sheets_count_column_values", "75": "MCP_Google_Sheets_set_formula", "76": "MCP_Jira_&_Confluence_get_issue", "77": "MCP_Google_Sheets_update_row", "78": "MCP_Google_Document_get_document", "79": "MCP_Gmail_create_draft", "80": "MCP_SDR_Management_get_campaign", "81": "MCP_SDR_Management_create_customers", "82": "MCP_Apollo_IO_people_enrichment", "83": "MCP_Apollo_IO_people_search", "84": "MCP_SDR_Management_list_products", "85": "MCP_SDR_Management_create_email_conversation", "86": "MCP_SDR_Management_get_email_conversations", "87": "MCP_SDR_Management_reply_email_from_customer", "88": "MCP_SDR_Management_fetch_customer"}