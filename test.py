from agent import agent
# from tools import post_processing
import json

task = "Craft a workflow to create a short video script from a given topic, with a specific duration of one minute. Use this script to generate audio using Eleven Labs with the voice ID TX3LPaxmHKxFdv7VOQHJ, and then fetch the generated audio file."

result = agent(task)
result = result.message["content"][0]["text"]
if isinstance(result, str):
    if "```json" in result:
        result = result.split("```json")[1].split("```")[0]
    result = json.loads(result)
# result["workflow_proper"] = post_processing(result["workflow"])
with open("test.json", "w") as f:
    json.dump(result, f)
