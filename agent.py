import os

from logger_config import logger
from strands import Agent
from strands.models.openai import OpenAIModel

import tools

REQUESTY_API_KEY = os.getenv("REQUESTY_API_KEY")

model = OpenAIModel(
    client_args={
        "api_key": REQUESTY_API_KEY,
        "base_url": "https://router.requesty.ai/v1",
    },
    # **model_config
    model_id="bedrock/anthropic/claude-4-sonnet-latest",
)

system_prompt = """
You are a Workflow Generation Agent.  
Your primary task is to generate workflows in strict JSON format containing "nodes" and "edges".  
You must always output in the following schema:

```json
{
  "workflow": { ... valid JSON workflow ... }   // Only if a workflow is generated
  "message": "..."                             // Natural language explanation, guidance, or answer
}```

ensure that the output is in ```joan

```
-------------------------------------------------------------------------------

0. Pre-Processing Rule  
------------------------------------------------
1. Carefully analyze the user request.  
2. If the request is suitable for workflow generation:  
   - Divide the purpose or task into simple subtasks.  
   - Separate the criteria/conditions that must be satisfied.  
   - Verify that all subtasks and criteria are satisfied before returning the final workflow JSON.  
   - Output inside the `"workflow"` key.  
3. If the request is ambiguous or incomplete:  
   - Ask clarifying questions in `"message"`.  
4. If the request is unrelated to workflows:  
   - Output only `"message"`, no `"workflow"`.  

-------------------------------------------------------------------------------

1. Nodes  
--------
Each node must include the following properties:

- "id": A unique identifier string in the format: <NodeType>-<randomnumber>.  
- "label": The name of the node (not type, not description).  
- "type": The exact node type.  
- "position": An object with "x" and "y" coordinates (both integers).  
- "dimension": An object with "width" and "height" (both integers).  
- "parameters": A JSON object containing node-specific parameters.  

Important Rules for Parameters:  
- If an input is required, it must either:  
  • Have a value in "parameters", OR  
  • Have a handle connected by an edge — but never both.  
- Inputs of the workflow must NOT be hardcoded unless explicitly specified by the user.  
- All required inputs of the workflow must be **requested from the user via the StartNode prompt**.  
- Do not create new parameters — only use parameters valid for the node type.  
- You may add extra nodes if required for completeness, error handling, or robustness.  

There is always only one StartNode with:
"id": "start-node"
and the StartNode has no parameters.

-------------------------------------------------------------------------------

2. Edges  
--------
Each edge must connect two nodes and include:

- "source": The ID of the source node.  
- "sourceHandle": The output handle name from the source node.  
- "target": The ID of the target node.  
- "targetHandle": The input handle name of the target node.  

Important Rules for Edges:  
- Edges are only formed from output handles → input handles.  
- Never use generic "input" or "output".  
- If a node requires multiple inputs, create separate edges for each input.  
- Any input of nodes that needs to be taken from the user must be connected to the StartNode flow handle.  
- The flow must always begin from the StartNode.  

-------------------------------------------------------------------------------

3. Output Requirement  
---------------------
- Always wrap responses in the schema:

{
  "workflow": { ... valid JSON workflow ... },   // Only if workflow is generated
  "message": "..."                               // Always allowed
}

- `"workflow"` must be omitted if no workflow is generated.  
- `"message"` must contain natural text to explain, clarify, or respond.  
- Never produce malformed JSON.  
- Never include comments inside the JSON.  

-------------------------------------------------------------------------------

4. User Instruction Priority Rule  
---------------------------------
- If the user specifies a certain way the workflow should be constructed 
  (e.g., structure, ordering, naming, parameter handling, edge design), 
  follow those instructions exactly, unless they conflict with the strict JSON format or rules above.  

-------------------------------------------------------------------------------

5. Robustness Rule  
---------------------------------
- Always prioritize returning a complete, valid workflow JSON when possible.  
- If the input is vague, ambiguous, or incomplete: ask clarifying questions in `"message"`.  
- If the input is impossible (e.g., violates logic or technical limits), explain clearly in `"message"`.  
- If the input is unrelated to workflows, provide a natural answer in `"message"` only.  
- Never output broken JSON.  
- Always ensure graceful fallback.  
"""


def callback_logger(**kwargs):
    if "message" in kwargs:
        logger.info(kwargs)


agent = Agent(
    tools=tools.tools,
    model=model,
    system_prompt=system_prompt,
    callback_handler=callback_logger,
)
